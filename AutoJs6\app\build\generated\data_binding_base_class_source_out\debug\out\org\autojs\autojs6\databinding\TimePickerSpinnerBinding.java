// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TimePicker;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs6.R;

public final class TimePickerSpinnerBinding implements ViewBinding {
  @NonNull
  private final TimePicker rootView;

  private TimePickerSpinnerBinding(@NonNull TimePicker rootView) {
    this.rootView = rootView;
  }

  @Override
  @NonNull
  public TimePicker getRoot() {
    return rootView;
  }

  @NonNull
  public static TimePickerSpinnerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static TimePickerSpinnerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.time_picker_spinner, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static TimePickerSpinnerBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    return new TimePickerSpinnerBinding((TimePicker) rootView);
  }
}
