package com.bm.atool.autojs.engine;

import android.content.Context;
import android.util.Log;

import com.bm.atool.autojs.api.APIBridge;
import com.bm.atool.autojs.model.ExecutionContext;
import com.bm.atool.autojs.model.Script;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * AutoJS 6 引擎适配器 - 简化版本
 * 使用Rhino JavaScript引擎提供脚本执行功能
 */
public class AutoJs6Engine {
    private static final String TAG = "AutoJs6Engine";
    
    private Context context;
    private APIBridge apiBridge;
    private boolean isInitialized = false;
    
    // 使用简化的JavaScript引擎
    private org.mozilla.javascript.Context rhinoContext;
    private org.mozilla.javascript.Scriptable globalScope;
    
    public AutoJs6Engine(Context context, APIBridge apiBridge) {
        this.context = context;
        this.apiBridge = apiBridge;
        
        initializeEngine();
    }
    
    /**
     * 初始化JavaScript引擎
     */
    private void initializeEngine() {
        try {
            Log.d(TAG, "Initializing JavaScript engine...");
            
            // 初始化Rhino引擎
            rhinoContext = org.mozilla.javascript.Context.enter();
            rhinoContext.setOptimizationLevel(-1); // 解释模式，支持调试
            
            // 创建全局作用域
            globalScope = rhinoContext.initStandardObjects();
            
            // 注入API
            injectAPIs();
            
            isInitialized = true;
            Log.d(TAG, "JavaScript engine initialized successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing JavaScript engine", e);
            isInitialized = false;
        } finally {
            if (rhinoContext != null) {
                org.mozilla.javascript.Context.exit();
            }
        }
    }
    
    /**
     * 注入API到JavaScript环境
     */
    private void injectAPIs() {
        try {
            // 注入console对象
            String consoleScript = 
                "var console = {" +
                "  log: function(msg) { " +
                "    java.lang.System.out.println('[JS] ' + msg); " +
                "  }," +
                "  error: function(msg) { " +
                "    java.lang.System.err.println('[JS ERROR] ' + msg); " +
                "  }," +
                "  warn: function(msg) { " +
                "    java.lang.System.out.println('[JS WARN] ' + msg); " +
                "  }" +
                "};";
            
            rhinoContext.evaluateString(globalScope, consoleScript, "console_init", 1, null);
            
            // 注入utils对象
            String utilsScript =
                "var utils = {" +
                "  log: function(msg) { " +
                "    console.log(msg); " +
                "  }," +
                "  sleep: function(ms) { " +
                "    try { java.lang.Thread.sleep(ms); } catch(e) { console.error('Sleep error: ' + e); }" +
                "  }," +
                "  toast: function(msg) { " +
                "    console.log('Toast: ' + msg);" +
                "  }" +
                "};";
            
            rhinoContext.evaluateString(globalScope, utilsScript, "utils_init", 1, null);
            
            Log.d(TAG, "APIs injected successfully");
            
        } catch (Exception e) {
            Log.e(TAG, "Error injecting APIs", e);
        }
    }
    
    /**
     * 执行脚本
     */
    public String execute(Script script, ExecutionContext executionContext) throws Exception {
        if (!isInitialized) {
            throw new IllegalStateException("JavaScript engine not initialized");
        }
        
        if (script == null || script.getContent() == null) {
            throw new IllegalArgumentException("Script or script content is null");
        }
        
        org.mozilla.javascript.Context execContext = null;
        try {
            Log.d(TAG, "Executing script: " + script.getName());
            
            // 进入JavaScript执行上下文
            execContext = org.mozilla.javascript.Context.enter();
            execContext.setOptimizationLevel(-1);
            
            // 创建脚本专用作用域
            org.mozilla.javascript.Scriptable scriptScope = execContext.newObject(globalScope);
            scriptScope.setPrototype(globalScope);
            scriptScope.setParentScope(null);
            
            // 执行脚本
            Object result = execContext.evaluateString(scriptScope, script.getContent(), 
                    script.getName(), 1, null);
            
            String resultString = org.mozilla.javascript.Context.toString(result);
            
            Log.d(TAG, "Script executed successfully: " + script.getName());
            return resultString;
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing script: " + script.getName(), e);
            throw e;
        } finally {
            if (execContext != null) {
                org.mozilla.javascript.Context.exit();
            }
        }
    }
    
    /**
     * 检查引擎是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 停止所有脚本
     */
    public void stopAllScripts() {
        try {
            Log.d(TAG, "Stopping all scripts");
            // 简化版本：由于使用同步执行，这里主要是日志记录
        } catch (Exception e) {
            Log.e(TAG, "Error stopping all scripts", e);
        }
    }
    
    /**
     * 获取运行中的脚本数量
     */
    public int getRunningScriptCount() {
        // 简化版本：由于使用同步执行，返回0
        return 0;
    }
    
    /**
     * 检查无障碍服务是否可用
     */
    public boolean isAccessibilityServiceEnabled() {
        try {
            // 这里可以添加实际的无障碍服务检查逻辑
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking accessibility service", e);
            return false;
        }
    }
    
    /**
     * 关闭引擎
     */
    public void shutdown() {
        try {
            Log.d(TAG, "Shutting down AutoJs6Engine...");
            
            // 停止所有脚本
            stopAllScripts();
            
            // 清理资源
            if (rhinoContext != null) {
                try {
                    org.mozilla.javascript.Context.exit();
                } catch (Exception e) {
                    // 忽略退出时的异常
                }
            }
            
            isInitialized = false;
            
            Log.d(TAG, "AutoJs6Engine shutdown completed");
            
        } catch (Exception e) {
            Log.e(TAG, "Error during AutoJs6Engine shutdown", e);
        }
    }
}
