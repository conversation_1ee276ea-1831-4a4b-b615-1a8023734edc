// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.core.ui.widget.JsCheckBox;
import org.autojs.autojs.theme.widget.ThemeColorFloatingActionButton;
import org.autojs.autojs.theme.widget.ThemeColorTextInputLayout;
import org.autojs.autojs.theme.widget.ThemeColorToolbar;
import org.autojs.autojs.ui.widget.PackageNameEditTextView;
import org.autojs.autojs6.R;

public final class ActivityProjectConfigBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView appIcon;

  @NonNull
  public final AppCompatEditText appName;

  @NonNull
  public final JsCheckBox defaultStableMode;

  @NonNull
  public final ThemeColorFloatingActionButton fab;

  @NonNull
  public final AppCompatEditText mainFileName;

  @NonNull
  public final PackageNameEditTextView packageName;

  @NonNull
  public final AppCompatEditText projectLocation;

  @NonNull
  public final ThemeColorTextInputLayout projectLocationWrapper;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final ThemeColorToolbar toolbar;

  @NonNull
  public final AppCompatEditText versionCode;

  @NonNull
  public final AppCompatEditText versionName;

  private ActivityProjectConfigBinding(@NonNull LinearLayout rootView, @NonNull ImageView appIcon,
      @NonNull AppCompatEditText appName, @NonNull JsCheckBox defaultStableMode,
      @NonNull ThemeColorFloatingActionButton fab, @NonNull AppCompatEditText mainFileName,
      @NonNull PackageNameEditTextView packageName, @NonNull AppCompatEditText projectLocation,
      @NonNull ThemeColorTextInputLayout projectLocationWrapper, @NonNull ScrollView scrollView,
      @NonNull ThemeColorToolbar toolbar, @NonNull AppCompatEditText versionCode,
      @NonNull AppCompatEditText versionName) {
    this.rootView = rootView;
    this.appIcon = appIcon;
    this.appName = appName;
    this.defaultStableMode = defaultStableMode;
    this.fab = fab;
    this.mainFileName = mainFileName;
    this.packageName = packageName;
    this.projectLocation = projectLocation;
    this.projectLocationWrapper = projectLocationWrapper;
    this.scrollView = scrollView;
    this.toolbar = toolbar;
    this.versionCode = versionCode;
    this.versionName = versionName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProjectConfigBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProjectConfigBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_project_config, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProjectConfigBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_icon;
      ImageView appIcon = ViewBindings.findChildViewById(rootView, id);
      if (appIcon == null) {
        break missingId;
      }

      id = R.id.app_name;
      AppCompatEditText appName = ViewBindings.findChildViewById(rootView, id);
      if (appName == null) {
        break missingId;
      }

      id = R.id.default_stable_mode;
      JsCheckBox defaultStableMode = ViewBindings.findChildViewById(rootView, id);
      if (defaultStableMode == null) {
        break missingId;
      }

      id = R.id.fab;
      ThemeColorFloatingActionButton fab = ViewBindings.findChildViewById(rootView, id);
      if (fab == null) {
        break missingId;
      }

      id = R.id.main_file_name;
      AppCompatEditText mainFileName = ViewBindings.findChildViewById(rootView, id);
      if (mainFileName == null) {
        break missingId;
      }

      id = R.id.package_name;
      PackageNameEditTextView packageName = ViewBindings.findChildViewById(rootView, id);
      if (packageName == null) {
        break missingId;
      }

      id = R.id.project_location;
      AppCompatEditText projectLocation = ViewBindings.findChildViewById(rootView, id);
      if (projectLocation == null) {
        break missingId;
      }

      id = R.id.project_location_wrapper;
      ThemeColorTextInputLayout projectLocationWrapper = ViewBindings.findChildViewById(rootView, id);
      if (projectLocationWrapper == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.version_code;
      AppCompatEditText versionCode = ViewBindings.findChildViewById(rootView, id);
      if (versionCode == null) {
        break missingId;
      }

      id = R.id.version_name;
      AppCompatEditText versionName = ViewBindings.findChildViewById(rootView, id);
      if (versionName == null) {
        break missingId;
      }

      return new ActivityProjectConfigBinding((LinearLayout) rootView, appIcon, appName,
          defaultStableMode, fab, mainFileName, packageName, projectLocation,
          projectLocationWrapper, scrollView, toolbar, versionCode, versionName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
