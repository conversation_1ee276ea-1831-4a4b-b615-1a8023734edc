// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class EditableFileInfoDialogListItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView byteCountColon;

  @NonNull
  public final Guideline byteCountGuideline;

  @NonNull
  public final TextView byteCountLabel;

  @NonNull
  public final ConstraintLayout byteCountParent;

  @NonNull
  public final TextView byteCountValue;

  @NonNull
  public final TextView charCountColon;

  @NonNull
  public final Guideline charCountGuideline;

  @NonNull
  public final TextView charCountLabel;

  @NonNull
  public final ConstraintLayout charCountParent;

  @NonNull
  public final TextView charCountValue;

  @NonNull
  public final TextView fileCharsetColon;

  @NonNull
  public final Guideline fileCharsetGuideline;

  @NonNull
  public final TextView fileCharsetLabel;

  @NonNull
  public final ConstraintLayout fileCharsetParent;

  @NonNull
  public final TextView fileCharsetValue;

  @NonNull
  public final TextView filePathColon;

  @NonNull
  public final Guideline filePathGuideline;

  @NonNull
  public final TextView filePathLabel;

  @NonNull
  public final ConstraintLayout filePathParent;

  @NonNull
  public final TextView filePathValue;

  @NonNull
  public final TextView fileSizeColon;

  @NonNull
  public final Guideline fileSizeGuideline;

  @NonNull
  public final TextView fileSizeLabel;

  @NonNull
  public final ConstraintLayout fileSizeParent;

  @NonNull
  public final TextView fileSizeValue;

  @NonNull
  public final TextView lineBreakColon;

  @NonNull
  public final Guideline lineBreakGuideline;

  @NonNull
  public final TextView lineBreakLabel;

  @NonNull
  public final ConstraintLayout lineBreakParent;

  @NonNull
  public final TextView lineBreakValue;

  @NonNull
  public final TextView lineCountColon;

  @NonNull
  public final Guideline lineCountGuideline;

  @NonNull
  public final TextView lineCountLabel;

  @NonNull
  public final ConstraintLayout lineCountParent;

  @NonNull
  public final TextView lineCountValue;

  @NonNull
  public final View splitLine;

  private EditableFileInfoDialogListItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView byteCountColon, @NonNull Guideline byteCountGuideline,
      @NonNull TextView byteCountLabel, @NonNull ConstraintLayout byteCountParent,
      @NonNull TextView byteCountValue, @NonNull TextView charCountColon,
      @NonNull Guideline charCountGuideline, @NonNull TextView charCountLabel,
      @NonNull ConstraintLayout charCountParent, @NonNull TextView charCountValue,
      @NonNull TextView fileCharsetColon, @NonNull Guideline fileCharsetGuideline,
      @NonNull TextView fileCharsetLabel, @NonNull ConstraintLayout fileCharsetParent,
      @NonNull TextView fileCharsetValue, @NonNull TextView filePathColon,
      @NonNull Guideline filePathGuideline, @NonNull TextView filePathLabel,
      @NonNull ConstraintLayout filePathParent, @NonNull TextView filePathValue,
      @NonNull TextView fileSizeColon, @NonNull Guideline fileSizeGuideline,
      @NonNull TextView fileSizeLabel, @NonNull ConstraintLayout fileSizeParent,
      @NonNull TextView fileSizeValue, @NonNull TextView lineBreakColon,
      @NonNull Guideline lineBreakGuideline, @NonNull TextView lineBreakLabel,
      @NonNull ConstraintLayout lineBreakParent, @NonNull TextView lineBreakValue,
      @NonNull TextView lineCountColon, @NonNull Guideline lineCountGuideline,
      @NonNull TextView lineCountLabel, @NonNull ConstraintLayout lineCountParent,
      @NonNull TextView lineCountValue, @NonNull View splitLine) {
    this.rootView = rootView;
    this.byteCountColon = byteCountColon;
    this.byteCountGuideline = byteCountGuideline;
    this.byteCountLabel = byteCountLabel;
    this.byteCountParent = byteCountParent;
    this.byteCountValue = byteCountValue;
    this.charCountColon = charCountColon;
    this.charCountGuideline = charCountGuideline;
    this.charCountLabel = charCountLabel;
    this.charCountParent = charCountParent;
    this.charCountValue = charCountValue;
    this.fileCharsetColon = fileCharsetColon;
    this.fileCharsetGuideline = fileCharsetGuideline;
    this.fileCharsetLabel = fileCharsetLabel;
    this.fileCharsetParent = fileCharsetParent;
    this.fileCharsetValue = fileCharsetValue;
    this.filePathColon = filePathColon;
    this.filePathGuideline = filePathGuideline;
    this.filePathLabel = filePathLabel;
    this.filePathParent = filePathParent;
    this.filePathValue = filePathValue;
    this.fileSizeColon = fileSizeColon;
    this.fileSizeGuideline = fileSizeGuideline;
    this.fileSizeLabel = fileSizeLabel;
    this.fileSizeParent = fileSizeParent;
    this.fileSizeValue = fileSizeValue;
    this.lineBreakColon = lineBreakColon;
    this.lineBreakGuideline = lineBreakGuideline;
    this.lineBreakLabel = lineBreakLabel;
    this.lineBreakParent = lineBreakParent;
    this.lineBreakValue = lineBreakValue;
    this.lineCountColon = lineCountColon;
    this.lineCountGuideline = lineCountGuideline;
    this.lineCountLabel = lineCountLabel;
    this.lineCountParent = lineCountParent;
    this.lineCountValue = lineCountValue;
    this.splitLine = splitLine;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static EditableFileInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static EditableFileInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.editable_file_info_dialog_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static EditableFileInfoDialogListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.byte_count_colon;
      TextView byteCountColon = ViewBindings.findChildViewById(rootView, id);
      if (byteCountColon == null) {
        break missingId;
      }

      id = R.id.byte_count_guideline;
      Guideline byteCountGuideline = ViewBindings.findChildViewById(rootView, id);
      if (byteCountGuideline == null) {
        break missingId;
      }

      id = R.id.byte_count_label;
      TextView byteCountLabel = ViewBindings.findChildViewById(rootView, id);
      if (byteCountLabel == null) {
        break missingId;
      }

      id = R.id.byte_count_parent;
      ConstraintLayout byteCountParent = ViewBindings.findChildViewById(rootView, id);
      if (byteCountParent == null) {
        break missingId;
      }

      id = R.id.byte_count_value;
      TextView byteCountValue = ViewBindings.findChildViewById(rootView, id);
      if (byteCountValue == null) {
        break missingId;
      }

      id = R.id.char_count_colon;
      TextView charCountColon = ViewBindings.findChildViewById(rootView, id);
      if (charCountColon == null) {
        break missingId;
      }

      id = R.id.char_count_guideline;
      Guideline charCountGuideline = ViewBindings.findChildViewById(rootView, id);
      if (charCountGuideline == null) {
        break missingId;
      }

      id = R.id.char_count_label;
      TextView charCountLabel = ViewBindings.findChildViewById(rootView, id);
      if (charCountLabel == null) {
        break missingId;
      }

      id = R.id.char_count_parent;
      ConstraintLayout charCountParent = ViewBindings.findChildViewById(rootView, id);
      if (charCountParent == null) {
        break missingId;
      }

      id = R.id.char_count_value;
      TextView charCountValue = ViewBindings.findChildViewById(rootView, id);
      if (charCountValue == null) {
        break missingId;
      }

      id = R.id.file_charset_colon;
      TextView fileCharsetColon = ViewBindings.findChildViewById(rootView, id);
      if (fileCharsetColon == null) {
        break missingId;
      }

      id = R.id.file_charset_guideline;
      Guideline fileCharsetGuideline = ViewBindings.findChildViewById(rootView, id);
      if (fileCharsetGuideline == null) {
        break missingId;
      }

      id = R.id.file_charset_label;
      TextView fileCharsetLabel = ViewBindings.findChildViewById(rootView, id);
      if (fileCharsetLabel == null) {
        break missingId;
      }

      id = R.id.file_charset_parent;
      ConstraintLayout fileCharsetParent = ViewBindings.findChildViewById(rootView, id);
      if (fileCharsetParent == null) {
        break missingId;
      }

      id = R.id.file_charset_value;
      TextView fileCharsetValue = ViewBindings.findChildViewById(rootView, id);
      if (fileCharsetValue == null) {
        break missingId;
      }

      id = R.id.file_path_colon;
      TextView filePathColon = ViewBindings.findChildViewById(rootView, id);
      if (filePathColon == null) {
        break missingId;
      }

      id = R.id.file_path_guideline;
      Guideline filePathGuideline = ViewBindings.findChildViewById(rootView, id);
      if (filePathGuideline == null) {
        break missingId;
      }

      id = R.id.file_path_label;
      TextView filePathLabel = ViewBindings.findChildViewById(rootView, id);
      if (filePathLabel == null) {
        break missingId;
      }

      id = R.id.file_path_parent;
      ConstraintLayout filePathParent = ViewBindings.findChildViewById(rootView, id);
      if (filePathParent == null) {
        break missingId;
      }

      id = R.id.file_path_value;
      TextView filePathValue = ViewBindings.findChildViewById(rootView, id);
      if (filePathValue == null) {
        break missingId;
      }

      id = R.id.file_size_colon;
      TextView fileSizeColon = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeColon == null) {
        break missingId;
      }

      id = R.id.file_size_guideline;
      Guideline fileSizeGuideline = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeGuideline == null) {
        break missingId;
      }

      id = R.id.file_size_label;
      TextView fileSizeLabel = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeLabel == null) {
        break missingId;
      }

      id = R.id.file_size_parent;
      ConstraintLayout fileSizeParent = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeParent == null) {
        break missingId;
      }

      id = R.id.file_size_value;
      TextView fileSizeValue = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeValue == null) {
        break missingId;
      }

      id = R.id.line_break_colon;
      TextView lineBreakColon = ViewBindings.findChildViewById(rootView, id);
      if (lineBreakColon == null) {
        break missingId;
      }

      id = R.id.line_break_guideline;
      Guideline lineBreakGuideline = ViewBindings.findChildViewById(rootView, id);
      if (lineBreakGuideline == null) {
        break missingId;
      }

      id = R.id.line_break_label;
      TextView lineBreakLabel = ViewBindings.findChildViewById(rootView, id);
      if (lineBreakLabel == null) {
        break missingId;
      }

      id = R.id.line_break_parent;
      ConstraintLayout lineBreakParent = ViewBindings.findChildViewById(rootView, id);
      if (lineBreakParent == null) {
        break missingId;
      }

      id = R.id.line_break_value;
      TextView lineBreakValue = ViewBindings.findChildViewById(rootView, id);
      if (lineBreakValue == null) {
        break missingId;
      }

      id = R.id.line_count_colon;
      TextView lineCountColon = ViewBindings.findChildViewById(rootView, id);
      if (lineCountColon == null) {
        break missingId;
      }

      id = R.id.line_count_guideline;
      Guideline lineCountGuideline = ViewBindings.findChildViewById(rootView, id);
      if (lineCountGuideline == null) {
        break missingId;
      }

      id = R.id.line_count_label;
      TextView lineCountLabel = ViewBindings.findChildViewById(rootView, id);
      if (lineCountLabel == null) {
        break missingId;
      }

      id = R.id.line_count_parent;
      ConstraintLayout lineCountParent = ViewBindings.findChildViewById(rootView, id);
      if (lineCountParent == null) {
        break missingId;
      }

      id = R.id.line_count_value;
      TextView lineCountValue = ViewBindings.findChildViewById(rootView, id);
      if (lineCountValue == null) {
        break missingId;
      }

      id = R.id.split_line;
      View splitLine = ViewBindings.findChildViewById(rootView, id);
      if (splitLine == null) {
        break missingId;
      }

      return new EditableFileInfoDialogListItemBinding((ConstraintLayout) rootView, byteCountColon,
          byteCountGuideline, byteCountLabel, byteCountParent, byteCountValue, charCountColon,
          charCountGuideline, charCountLabel, charCountParent, charCountValue, fileCharsetColon,
          fileCharsetGuideline, fileCharsetLabel, fileCharsetParent, fileCharsetValue,
          filePathColon, filePathGuideline, filePathLabel, filePathParent, filePathValue,
          fileSizeColon, fileSizeGuideline, fileSizeLabel, fileSizeParent, fileSizeValue,
          lineBreakColon, lineBreakGuideline, lineBreakLabel, lineBreakParent, lineBreakValue,
          lineCountColon, lineCountGuideline, lineCountLabel, lineCountParent, lineCountValue,
          splitLine);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
