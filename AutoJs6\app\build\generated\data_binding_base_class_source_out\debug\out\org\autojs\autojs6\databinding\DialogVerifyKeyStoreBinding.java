// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorTextInputLayout;
import org.autojs.autojs6.R;

public final class DialogVerifyKeyStoreBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextInputEditText alias;

  @NonNull
  public final TextInputEditText aliasPassword;

  @NonNull
  public final ThemeColorTextInputLayout aliasPasswordTextInputLayout;

  @NonNull
  public final ThemeColorTextInputLayout aliasTextInputLayout;

  @NonNull
  public final Button cancel;

  @NonNull
  public final LinearLayout containerVerifyState;

  @NonNull
  public final TextView filePath;

  @NonNull
  public final ImageView imgVerifyState;

  @NonNull
  public final TextInputEditText password;

  @NonNull
  public final ThemeColorTextInputLayout passwordTextInputLayout;

  @NonNull
  public final TextView textVerifyState;

  @NonNull
  public final Button verify;

  private DialogVerifyKeyStoreBinding(@NonNull CardView rootView, @NonNull TextInputEditText alias,
      @NonNull TextInputEditText aliasPassword,
      @NonNull ThemeColorTextInputLayout aliasPasswordTextInputLayout,
      @NonNull ThemeColorTextInputLayout aliasTextInputLayout, @NonNull Button cancel,
      @NonNull LinearLayout containerVerifyState, @NonNull TextView filePath,
      @NonNull ImageView imgVerifyState, @NonNull TextInputEditText password,
      @NonNull ThemeColorTextInputLayout passwordTextInputLayout, @NonNull TextView textVerifyState,
      @NonNull Button verify) {
    this.rootView = rootView;
    this.alias = alias;
    this.aliasPassword = aliasPassword;
    this.aliasPasswordTextInputLayout = aliasPasswordTextInputLayout;
    this.aliasTextInputLayout = aliasTextInputLayout;
    this.cancel = cancel;
    this.containerVerifyState = containerVerifyState;
    this.filePath = filePath;
    this.imgVerifyState = imgVerifyState;
    this.password = password;
    this.passwordTextInputLayout = passwordTextInputLayout;
    this.textVerifyState = textVerifyState;
    this.verify = verify;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogVerifyKeyStoreBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogVerifyKeyStoreBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_verify_key_store, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogVerifyKeyStoreBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.alias;
      TextInputEditText alias = ViewBindings.findChildViewById(rootView, id);
      if (alias == null) {
        break missingId;
      }

      id = R.id.alias_password;
      TextInputEditText aliasPassword = ViewBindings.findChildViewById(rootView, id);
      if (aliasPassword == null) {
        break missingId;
      }

      id = R.id.alias_password_text_input_layout;
      ThemeColorTextInputLayout aliasPasswordTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (aliasPasswordTextInputLayout == null) {
        break missingId;
      }

      id = R.id.alias_text_input_layout;
      ThemeColorTextInputLayout aliasTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (aliasTextInputLayout == null) {
        break missingId;
      }

      id = R.id.cancel;
      Button cancel = ViewBindings.findChildViewById(rootView, id);
      if (cancel == null) {
        break missingId;
      }

      id = R.id.container_verify_state;
      LinearLayout containerVerifyState = ViewBindings.findChildViewById(rootView, id);
      if (containerVerifyState == null) {
        break missingId;
      }

      id = R.id.file_path;
      TextView filePath = ViewBindings.findChildViewById(rootView, id);
      if (filePath == null) {
        break missingId;
      }

      id = R.id.img_verify_state;
      ImageView imgVerifyState = ViewBindings.findChildViewById(rootView, id);
      if (imgVerifyState == null) {
        break missingId;
      }

      id = R.id.password;
      TextInputEditText password = ViewBindings.findChildViewById(rootView, id);
      if (password == null) {
        break missingId;
      }

      id = R.id.password_text_input_layout;
      ThemeColorTextInputLayout passwordTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (passwordTextInputLayout == null) {
        break missingId;
      }

      id = R.id.text_verify_state;
      TextView textVerifyState = ViewBindings.findChildViewById(rootView, id);
      if (textVerifyState == null) {
        break missingId;
      }

      id = R.id.verify;
      Button verify = ViewBindings.findChildViewById(rootView, id);
      if (verify == null) {
        break missingId;
      }

      return new DialogVerifyKeyStoreBinding((CardView) rootView, alias, aliasPassword,
          aliasPasswordTextInputLayout, aliasTextInputLayout, cancel, containerVerifyState,
          filePath, imgVerifyState, password, passwordTextInputLayout, textVerifyState, verify);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
