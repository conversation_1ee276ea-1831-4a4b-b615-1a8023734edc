// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.core.ui.widget.JsActionMenuView;
import org.autojs.autojs6.R;

public final class JsActionmenuviewBinding implements ViewBinding {
  @NonNull
  private final JsActionMenuView rootView;

  private JsActionmenuviewBinding(@NonNull JsActionMenuView rootView) {
    this.rootView = rootView;
  }

  @Override
  @NonNull
  public JsActionMenuView getRoot() {
    return rootView;
  }

  @NonNull
  public static JsActionmenuviewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static JsActionmenuviewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.js_actionmenuview, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static JsActionmenuviewBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    return new JsActionmenuviewBinding((JsActionMenuView) rootView);
  }
}
