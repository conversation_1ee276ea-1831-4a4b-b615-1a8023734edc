// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorToolbar;
import org.autojs.autojs.ui.explorer.ExplorerView;
import org.autojs.autojs6.R;

public final class ActivityScriptWidgetSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ExplorerView scriptList;

  @NonNull
  public final ThemeColorToolbar toolbar;

  private ActivityScriptWidgetSettingsBinding(@NonNull LinearLayout rootView,
      @NonNull ExplorerView scriptList, @NonNull ThemeColorToolbar toolbar) {
    this.rootView = rootView;
    this.scriptList = scriptList;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityScriptWidgetSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityScriptWidgetSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_script_widget_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityScriptWidgetSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.script_list;
      ExplorerView scriptList = ViewBindings.findChildViewById(rootView, id);
      if (scriptList == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityScriptWidgetSettingsBinding((LinearLayout) rootView, scriptList, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
