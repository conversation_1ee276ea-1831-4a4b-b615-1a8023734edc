// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.floating.CircularActionMenu;
import org.autojs.autojs6.R;

public final class CircularActionMenuBinding implements ViewBinding {
  @NonNull
  private final CircularActionMenu rootView;

  @NonNull
  public final ImageView actionMenuMore;

  @NonNull
  public final ImageView layoutInspect;

  @NonNull
  public final ImageView record;

  @NonNull
  public final ImageView scriptList;

  @NonNull
  public final ImageView stopAllScripts;

  private CircularActionMenuBinding(@NonNull CircularActionMenu rootView,
      @NonNull ImageView actionMenuMore, @NonNull ImageView layoutInspect,
      @NonNull ImageView record, @NonNull ImageView scriptList, @NonNull ImageView stopAllScripts) {
    this.rootView = rootView;
    this.actionMenuMore = actionMenuMore;
    this.layoutInspect = layoutInspect;
    this.record = record;
    this.scriptList = scriptList;
    this.stopAllScripts = stopAllScripts;
  }

  @Override
  @NonNull
  public CircularActionMenu getRoot() {
    return rootView;
  }

  @NonNull
  public static CircularActionMenuBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CircularActionMenuBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.circular_action_menu, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CircularActionMenuBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.action_menu_more;
      ImageView actionMenuMore = ViewBindings.findChildViewById(rootView, id);
      if (actionMenuMore == null) {
        break missingId;
      }

      id = R.id.layout_inspect;
      ImageView layoutInspect = ViewBindings.findChildViewById(rootView, id);
      if (layoutInspect == null) {
        break missingId;
      }

      id = R.id.record;
      ImageView record = ViewBindings.findChildViewById(rootView, id);
      if (record == null) {
        break missingId;
      }

      id = R.id.script_list;
      ImageView scriptList = ViewBindings.findChildViewById(rootView, id);
      if (scriptList == null) {
        break missingId;
      }

      id = R.id.stop_all_scripts;
      ImageView stopAllScripts = ViewBindings.findChildViewById(rootView, id);
      if (stopAllScripts == null) {
        break missingId;
      }

      return new CircularActionMenuBinding((CircularActionMenu) rootView, actionMenuMore,
          layoutInspect, record, scriptList, stopAllScripts);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
