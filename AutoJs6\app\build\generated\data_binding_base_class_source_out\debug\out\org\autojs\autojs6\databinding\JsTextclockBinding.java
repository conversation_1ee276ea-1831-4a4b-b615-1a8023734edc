// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.core.ui.widget.JsTextClock;
import org.autojs.autojs6.R;

public final class JsTextclockBinding implements ViewBinding {
  @NonNull
  private final JsTextClock rootView;

  private JsTextclockBinding(@NonNull JsTextClock rootView) {
    this.rootView = rootView;
  }

  @Override
  @NonNull
  public JsTextClock getRoot() {
    return rootView;
  }

  @NonNull
  public static JsTextclockBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static JsTextclockBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.js_textclock, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static JsTextclockBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    return new JsTextclockBinding((JsTextClock) rootView);
  }
}
