package com.bm.atool;

import com.bm.autojs6.module.model.Script;
import com.bm.autojs6.module.model.ScriptResult;

import org.junit.Test;
import org.junit.Before;

import static org.junit.Assert.*;

/**
 * AutoJs6 单元测试
 * 测试不依赖Android环境的基本功能
 */
public class AutoJs6UnitTest {
    
    @Test
    public void testScriptModel() {
        String name = "TestScript";
        String content = "console.log('Hello World');";
        
        Script script = new Script(name, content);
        
        assertEquals("Script name should match", name, script.getName());
        assertEquals("Script content should match", content, script.getContent());
        assertNotNull("Script create time should not be null", script.getCreateTime());
        assertTrue("Script create time should be recent", 
            System.currentTimeMillis() - script.getCreateTime() < 1000);
    }
    
    @Test
    public void testScriptWithDescription() {
        String name = "TestScript";
        String content = "console.log('Hello World');";
        String description = "This is a test script";
        
        Script script = new Script(name, content, description);
        
        assertEquals("Script name should match", name, script.getName());
        assertEquals("Script content should match", content, script.getContent());
        assertEquals("Script description should match", description, script.getDescription());
    }
    
    @Test
    public void testScriptResultSuccess() {
        String scriptId = "test_001";
        String result = "Script executed successfully";
        
        ScriptResult scriptResult = ScriptResult.success(scriptId, result);
        
        assertEquals("Script ID should match", scriptId, scriptResult.getScriptId());
        assertTrue("Result should be successful", scriptResult.isSuccess());
        assertEquals("Result content should match", result, scriptResult.getResult());
        assertNull("Error should be null for successful result", scriptResult.getError());
        assertTrue("Execute time should be recent", 
            System.currentTimeMillis() - scriptResult.getExecuteTime() < 1000);
    }
    
    @Test
    public void testScriptResultError() {
        String scriptId = "test_002";
        String error = "Script execution failed";
        
        ScriptResult scriptResult = ScriptResult.error(scriptId, error);
        
        assertEquals("Script ID should match", scriptId, scriptResult.getScriptId());
        assertFalse("Result should not be successful", scriptResult.isSuccess());
        assertEquals("Error message should match", error, scriptResult.getError());
        assertNull("Result should be null for error result", scriptResult.getResult());
        assertTrue("Execute time should be recent", 
            System.currentTimeMillis() - scriptResult.getExecuteTime() < 1000);
    }
    
    @Test
    public void testScriptToString() {
        String name = "TestScript";
        String content = "console.log('Hello World');";
        String description = "Test description";
        
        Script script = new Script(name, content, description);
        String toString = script.toString();
        
        assertTrue("toString should contain name", toString.contains(name));
        assertTrue("toString should contain description", toString.contains(description));
        assertNotNull("toString should not be null", toString);
        assertFalse("toString should not be empty", toString.isEmpty());
    }
    
    @Test
    public void testScriptResultToString() {
        String scriptId = "test_003";
        String result = "Test result";
        
        ScriptResult scriptResult = ScriptResult.success(scriptId, result);
        String toString = scriptResult.toString();
        
        assertTrue("toString should contain script ID", toString.contains(scriptId));
        assertTrue("toString should contain result", toString.contains(result));
        assertTrue("toString should contain success status", toString.contains("true"));
        assertNotNull("toString should not be null", toString);
        assertFalse("toString should not be empty", toString.isEmpty());
    }
    
    @Test
    public void testScriptSetters() {
        Script script = new Script("Original", "original content");
        
        String newName = "Updated Name";
        String newContent = "updated content";
        String newDescription = "updated description";
        long newCreateTime = System.currentTimeMillis() - 10000;
        
        script.setName(newName);
        script.setContent(newContent);
        script.setDescription(newDescription);
        script.setCreateTime(newCreateTime);
        
        assertEquals("Name should be updated", newName, script.getName());
        assertEquals("Content should be updated", newContent, script.getContent());
        assertEquals("Description should be updated", newDescription, script.getDescription());
        assertEquals("Create time should be updated", newCreateTime, script.getCreateTime());
    }
    
    @Test
    public void testScriptResultDuration() {
        String scriptId = "test_004";
        String result = "Test result";
        
        ScriptResult scriptResult = ScriptResult.success(scriptId, result);
        
        // 设置持续时间
        long duration = 1500; // 1.5秒
        scriptResult.setDuration(duration);
        
        assertEquals("Duration should match", duration, scriptResult.getDuration());
    }
    
    @Test
    public void testNullValues() {
        // 测试null值处理
        Script script = new Script(null, null);
        
        assertNull("Name should be null", script.getName());
        assertNull("Content should be null", script.getContent());
        assertNotNull("Create time should not be null", script.getCreateTime());
        
        // 测试ScriptResult的null处理
        ScriptResult successResult = ScriptResult.success("test", null);
        assertTrue("Should still be successful with null result", successResult.isSuccess());
        assertNull("Result should be null", successResult.getResult());
        
        ScriptResult errorResult = ScriptResult.error("test", null);
        assertFalse("Should not be successful", errorResult.isSuccess());
        assertNull("Error should be null", errorResult.getError());
    }
    
    @Test
    public void testEmptyValues() {
        // 测试空字符串处理
        Script script = new Script("", "");
        
        assertEquals("Name should be empty", "", script.getName());
        assertEquals("Content should be empty", "", script.getContent());
        
        ScriptResult result = ScriptResult.success("", "");
        assertEquals("Script ID should be empty", "", result.getScriptId());
        assertEquals("Result should be empty", "", result.getResult());
    }
}
