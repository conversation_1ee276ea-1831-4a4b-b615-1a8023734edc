{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4b1b6d71ac725a2fc4ab91a425e45a1c\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "7,8,9,10,11,12,13,39", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "334,404,488,572,668,770,872,3727", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "399,483,567,663,765,867,961,3811"}}, {"source": "F:\\work2025\\shanghai\\android-tool-v2\\android-tool-v2\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "102", "endLines": "6", "endColumns": "12", "endOffsets": "391"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "6", "endColumns": "12", "endOffsets": "329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40d8a17e80b34dfeac4d4324ceeb8814\\transformed\\material-1.11.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "966,1041,1152,1241,1342,1449,1556,1655,1762,1865,1953,2077,2179,2281,2397,2499,2613,2741,2857,2979,3115,3235,3369,3489,3601,3816,3933,4057,4187,4309,4447,4581,4697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1036,1147,1236,1337,1444,1551,1650,1757,1860,1948,2072,2174,2276,2392,2494,2608,2736,2852,2974,3110,3230,3364,3484,3596,3722,3928,4052,4182,4304,4442,4576,4692,4812"}}]}]}