// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.theme.widget.ThemeColorSwitch;
import org.autojs.autojs6.R;

public final class MtThemeColorCheckBoxPreferenceBinding implements ViewBinding {
  @NonNull
  private final ThemeColorSwitch rootView;

  @NonNull
  public final ThemeColorSwitch checkbox;

  private MtThemeColorCheckBoxPreferenceBinding(@NonNull ThemeColorSwitch rootView,
      @NonNull ThemeColorSwitch checkbox) {
    this.rootView = rootView;
    this.checkbox = checkbox;
  }

  @Override
  @NonNull
  public ThemeColorSwitch getRoot() {
    return rootView;
  }

  @NonNull
  public static MtThemeColorCheckBoxPreferenceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MtThemeColorCheckBoxPreferenceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.mt_theme_color_check_box_preference, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MtThemeColorCheckBoxPreferenceBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    ThemeColorSwitch checkbox = (ThemeColorSwitch) rootView;

    return new MtThemeColorCheckBoxPreferenceBinding((ThemeColorSwitch) rootView, checkbox);
  }
}
