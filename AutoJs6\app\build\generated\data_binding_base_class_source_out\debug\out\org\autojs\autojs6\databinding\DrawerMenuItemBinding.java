// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.zhanghai.android.materialprogressbar.MaterialProgressBar;
import org.autojs.autojs.ui.widget.PrefSwitch;
import org.autojs.autojs6.R;

public final class DrawerMenuItemBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView icon;

  @NonNull
  public final FrameLayout iconContainer;

  @NonNull
  public final MaterialProgressBar progressBar;

  @NonNull
  public final TextView subtitle;

  @NonNull
  public final PrefSwitch sw;

  @NonNull
  public final TextView title;

  private DrawerMenuItemBinding(@NonNull RelativeLayout rootView, @NonNull ImageView icon,
      @NonNull FrameLayout iconContainer, @NonNull MaterialProgressBar progressBar,
      @NonNull TextView subtitle, @NonNull PrefSwitch sw, @NonNull TextView title) {
    this.rootView = rootView;
    this.icon = icon;
    this.iconContainer = iconContainer;
    this.progressBar = progressBar;
    this.subtitle = subtitle;
    this.sw = sw;
    this.title = title;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DrawerMenuItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DrawerMenuItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.drawer_menu_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DrawerMenuItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.icon;
      ImageView icon = ViewBindings.findChildViewById(rootView, id);
      if (icon == null) {
        break missingId;
      }

      id = R.id.icon_container;
      FrameLayout iconContainer = ViewBindings.findChildViewById(rootView, id);
      if (iconContainer == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      MaterialProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.subtitle;
      TextView subtitle = ViewBindings.findChildViewById(rootView, id);
      if (subtitle == null) {
        break missingId;
      }

      id = R.id.sw;
      PrefSwitch sw = ViewBindings.findChildViewById(rootView, id);
      if (sw == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      return new DrawerMenuItemBinding((RelativeLayout) rootView, icon, iconContainer, progressBar,
          subtitle, sw, title);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
