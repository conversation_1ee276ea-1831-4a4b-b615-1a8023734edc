// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ExplorerProjectToolbarBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayoutCompat commands;

  @NonNull
  public final ImageView projectBuild;

  @NonNull
  public final ImageView projectEdit;

  @NonNull
  public final TextView projectName;

  @NonNull
  public final ImageView projectRun;

  private ExplorerProjectToolbarBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayoutCompat commands, @NonNull ImageView projectBuild,
      @NonNull ImageView projectEdit, @NonNull TextView projectName,
      @NonNull ImageView projectRun) {
    this.rootView = rootView;
    this.commands = commands;
    this.projectBuild = projectBuild;
    this.projectEdit = projectEdit;
    this.projectName = projectName;
    this.projectRun = projectRun;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ExplorerProjectToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExplorerProjectToolbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.explorer_project_toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExplorerProjectToolbarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.commands;
      LinearLayoutCompat commands = ViewBindings.findChildViewById(rootView, id);
      if (commands == null) {
        break missingId;
      }

      id = R.id.project_build;
      ImageView projectBuild = ViewBindings.findChildViewById(rootView, id);
      if (projectBuild == null) {
        break missingId;
      }

      id = R.id.project_edit;
      ImageView projectEdit = ViewBindings.findChildViewById(rootView, id);
      if (projectEdit == null) {
        break missingId;
      }

      id = R.id.project_name;
      TextView projectName = ViewBindings.findChildViewById(rootView, id);
      if (projectName == null) {
        break missingId;
      }

      id = R.id.project_run;
      ImageView projectRun = ViewBindings.findChildViewById(rootView, id);
      if (projectRun == null) {
        break missingId;
      }

      return new ExplorerProjectToolbarBinding((LinearLayout) rootView, commands, projectBuild,
          projectEdit, projectName, projectRun);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
