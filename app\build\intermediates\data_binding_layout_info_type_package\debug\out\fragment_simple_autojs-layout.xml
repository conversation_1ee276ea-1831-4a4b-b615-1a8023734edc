<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_simple_autojs" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_simple_autojs.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_simple_autojs_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="158" endOffset="12"/></Target><Target id="@+id/tvScriptStatus" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="51"/></Target><Target id="@+id/tvRunningScripts" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="47" endOffset="58"/></Target><Target id="@+id/etScriptName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="59" startOffset="12" endLine="64" endOffset="43"/></Target><Target id="@+id/etScriptContent" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="12" endLine="85" endOffset="58"/></Target><Target id="@+id/btnExecuteScript" view="Button"><Expressions/><location startLine="96" startOffset="12" endLine="104" endOffset="40"/></Target><Target id="@+id/btnStopAllScripts" view="Button"><Expressions/><location startLine="111" startOffset="16" endLine="120" endOffset="44"/></Target><Target id="@+id/btnCheckStatus" view="Button"><Expressions/><location startLine="122" startOffset="16" endLine="130" endOffset="44"/></Target></Targets></Layout>