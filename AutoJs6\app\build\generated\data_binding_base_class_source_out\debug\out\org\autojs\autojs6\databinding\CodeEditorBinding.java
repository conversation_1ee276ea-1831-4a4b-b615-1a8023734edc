// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.ui.edit.editor.CodeEditText;
import org.autojs.autojs6.R;

public final class CodeEditorBinding implements ViewBinding {
  @NonNull
  private final CodeEditText rootView;

  @NonNull
  public final CodeEditText codeEditText;

  private CodeEditorBinding(@NonNull CodeEditText rootView, @NonNull CodeEditText codeEditText) {
    this.rootView = rootView;
    this.codeEditText = codeEditText;
  }

  @Override
  @NonNull
  public CodeEditText getRoot() {
    return rootView;
  }

  @NonNull
  public static CodeEditorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CodeEditorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.code_editor, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CodeEditorBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    CodeEditText codeEditText = (CodeEditText) rootView;

    return new CodeEditorBinding((CodeEditText) rootView, codeEditText);
  }
}
