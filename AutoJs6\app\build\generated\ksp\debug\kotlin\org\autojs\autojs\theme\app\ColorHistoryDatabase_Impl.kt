package org.autojs.autojs.theme.app

import androidx.room.InvalidationTracker
import androidx.room.RoomOpenDelegate
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.room.util.TableInfo
import androidx.room.util.TableInfo.Companion.read
import androidx.room.util.dropFtsSyncTriggers
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Lazy
import kotlin.String
import kotlin.Suppress
import kotlin.collections.List
import kotlin.collections.Map
import kotlin.collections.MutableList
import kotlin.collections.MutableMap
import kotlin.collections.MutableSet
import kotlin.collections.Set
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.mutableSetOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class ColorHistoryDatabase_Impl : ColorHistoryDatabase() {
  private val _colorHistoryDao: Lazy<ColorHistoryDao> = lazy {
    ColorHistoryDao_Impl(this)
  }

  private val _paletteHistoryDao: Lazy<PaletteHistoryDao> = lazy {
    PaletteHistoryDao_Impl(this)
  }

  protected override fun createOpenDelegate(): RoomOpenDelegate {
    val _openDelegate: RoomOpenDelegate = object : RoomOpenDelegate(1,
        "d86daee113b5d8c7aca0ff092b8a1e65", "ca6343a43ff0c6dca8101d06fb073940") {
      public override fun createAllTables(connection: SQLiteConnection) {
        connection.execSQL("CREATE TABLE IF NOT EXISTS `color_history` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `library_id` INTEGER NOT NULL, `item_id` INTEGER NOT NULL, `last_used_time` INTEGER NOT NULL)")
        connection.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_color_history_library_id_item_id` ON `color_history` (`library_id`, `item_id`)")
        connection.execSQL("CREATE TABLE IF NOT EXISTS `palette_history` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `last_used_time` INTEGER NOT NULL, `hex` TEXT NOT NULL)")
        connection.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_palette_history_hex` ON `palette_history` (`hex`)")
        connection.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)")
        connection.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd86daee113b5d8c7aca0ff092b8a1e65')")
      }

      public override fun dropAllTables(connection: SQLiteConnection) {
        connection.execSQL("DROP TABLE IF EXISTS `color_history`")
        connection.execSQL("DROP TABLE IF EXISTS `palette_history`")
      }

      public override fun onCreate(connection: SQLiteConnection) {
      }

      public override fun onOpen(connection: SQLiteConnection) {
        internalInitInvalidationTracker(connection)
      }

      public override fun onPreMigrate(connection: SQLiteConnection) {
        dropFtsSyncTriggers(connection)
      }

      public override fun onPostMigrate(connection: SQLiteConnection) {
      }

      public override fun onValidateSchema(connection: SQLiteConnection):
          RoomOpenDelegate.ValidationResult {
        val _columnsColorHistory: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsColorHistory.put("id", TableInfo.Column("id", "INTEGER", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsColorHistory.put("library_id", TableInfo.Column("library_id", "INTEGER", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsColorHistory.put("item_id", TableInfo.Column("item_id", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsColorHistory.put("last_used_time", TableInfo.Column("last_used_time", "INTEGER",
            true, 0, null, TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysColorHistory: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesColorHistory: MutableSet<TableInfo.Index> = mutableSetOf()
        _indicesColorHistory.add(TableInfo.Index("index_color_history_library_id_item_id", true,
            listOf("library_id", "item_id"), listOf("ASC", "ASC")))
        val _infoColorHistory: TableInfo = TableInfo("color_history", _columnsColorHistory,
            _foreignKeysColorHistory, _indicesColorHistory)
        val _existingColorHistory: TableInfo = read(connection, "color_history")
        if (!_infoColorHistory.equals(_existingColorHistory)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |color_history(org.autojs.autojs.theme.app.ColorEntities.ColorHistory).
              | Expected:
              |""".trimMargin() + _infoColorHistory + """
              |
              | Found:
              |""".trimMargin() + _existingColorHistory)
        }
        val _columnsPaletteHistory: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsPaletteHistory.put("id", TableInfo.Column("id", "INTEGER", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsPaletteHistory.put("last_used_time", TableInfo.Column("last_used_time", "INTEGER",
            true, 0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsPaletteHistory.put("hex", TableInfo.Column("hex", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysPaletteHistory: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesPaletteHistory: MutableSet<TableInfo.Index> = mutableSetOf()
        _indicesPaletteHistory.add(TableInfo.Index("index_palette_history_hex", true, listOf("hex"),
            listOf("ASC")))
        val _infoPaletteHistory: TableInfo = TableInfo("palette_history", _columnsPaletteHistory,
            _foreignKeysPaletteHistory, _indicesPaletteHistory)
        val _existingPaletteHistory: TableInfo = read(connection, "palette_history")
        if (!_infoPaletteHistory.equals(_existingPaletteHistory)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |palette_history(org.autojs.autojs.theme.app.ColorEntities.PaletteHistory).
              | Expected:
              |""".trimMargin() + _infoPaletteHistory + """
              |
              | Found:
              |""".trimMargin() + _existingPaletteHistory)
        }
        return RoomOpenDelegate.ValidationResult(true, null)
      }
    }
    return _openDelegate
  }

  protected override fun createInvalidationTracker(): InvalidationTracker {
    val _shadowTablesMap: MutableMap<String, String> = mutableMapOf()
    val _viewTables: MutableMap<String, Set<String>> = mutableMapOf()
    return InvalidationTracker(this, _shadowTablesMap, _viewTables, "color_history",
        "palette_history")
  }

  public override fun clearAllTables() {
    super.performClear(false, "color_history", "palette_history")
  }

  protected override fun getRequiredTypeConverterClasses(): Map<KClass<*>, List<KClass<*>>> {
    val _typeConvertersMap: MutableMap<KClass<*>, List<KClass<*>>> = mutableMapOf()
    _typeConvertersMap.put(ColorHistoryDao::class, ColorHistoryDao_Impl.getRequiredConverters())
    _typeConvertersMap.put(PaletteHistoryDao::class, PaletteHistoryDao_Impl.getRequiredConverters())
    return _typeConvertersMap
  }

  public override fun getRequiredAutoMigrationSpecClasses(): Set<KClass<out AutoMigrationSpec>> {
    val _autoMigrationSpecsSet: MutableSet<KClass<out AutoMigrationSpec>> = mutableSetOf()
    return _autoMigrationSpecsSet
  }

  public override
      fun createAutoMigrations(autoMigrationSpecs: Map<KClass<out AutoMigrationSpec>, AutoMigrationSpec>):
      List<Migration> {
    val _autoMigrations: MutableList<Migration> = mutableListOf()
    return _autoMigrations
  }

  public override fun colorHistoryDao(): ColorHistoryDao = _colorHistoryDao.value

  public override fun paletteHistoryDao(): PaletteHistoryDao = _paletteHistoryDao.value
}
