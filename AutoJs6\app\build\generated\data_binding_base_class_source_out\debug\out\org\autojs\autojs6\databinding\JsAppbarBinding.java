// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.core.ui.widget.JsAppBarLayout;
import org.autojs.autojs6.R;

public final class JsAppbarBinding implements ViewBinding {
  @NonNull
  private final JsAppBarLayout rootView;

  private JsAppbarBinding(@NonNull JsAppBarLayout rootView) {
    this.rootView = rootView;
  }

  @Override
  @NonNull
  public JsAppBarLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static JsAppbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static JsAppbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.js_appbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static JsAppbarBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    return new JsAppbarBinding((JsAppBarLayout) rootView);
  }
}
