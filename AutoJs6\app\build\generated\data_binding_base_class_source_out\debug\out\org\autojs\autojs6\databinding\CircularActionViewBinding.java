// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.makeramen.roundedimageview.RoundedImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.floating.CircularActionView;
import org.autojs.autojs6.R;

public final class CircularActionViewBinding implements ViewBinding {
  @NonNull
  private final CircularActionView rootView;

  @NonNull
  public final RoundedImageView icon;

  private CircularActionViewBinding(@NonNull CircularActionView rootView,
      @NonNull RoundedImageView icon) {
    this.rootView = rootView;
    this.icon = icon;
  }

  @Override
  @NonNull
  public CircularActionView getRoot() {
    return rootView;
  }

  @NonNull
  public static CircularActionViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static CircularActionViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.circular_action_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static CircularActionViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.icon;
      RoundedImageView icon = ViewBindings.findChildViewById(rootView, id);
      if (icon == null) {
        break missingId;
      }

      return new CircularActionViewBinding((CircularActionView) rootView, icon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
