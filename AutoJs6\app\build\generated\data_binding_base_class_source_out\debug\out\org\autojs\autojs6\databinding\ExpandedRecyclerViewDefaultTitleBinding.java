// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorImageViewCompat;
import org.autojs.autojs6.R;

public final class ExpandedRecyclerViewDefaultTitleBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ThemeColorImageViewCompat expandHint;

  @NonNull
  public final ThemeColorImageViewCompat icon;

  @NonNull
  public final TextView title;

  private ExpandedRecyclerViewDefaultTitleBinding(@NonNull LinearLayout rootView,
      @NonNull ThemeColorImageViewCompat expandHint, @NonNull ThemeColorImageViewCompat icon,
      @NonNull TextView title) {
    this.rootView = rootView;
    this.expandHint = expandHint;
    this.icon = icon;
    this.title = title;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ExpandedRecyclerViewDefaultTitleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExpandedRecyclerViewDefaultTitleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.expanded_recycler_view_default_title, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExpandedRecyclerViewDefaultTitleBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.expand_hint;
      ThemeColorImageViewCompat expandHint = ViewBindings.findChildViewById(rootView, id);
      if (expandHint == null) {
        break missingId;
      }

      id = R.id.icon;
      ThemeColorImageViewCompat icon = ViewBindings.findChildViewById(rootView, id);
      if (icon == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      return new ExpandedRecyclerViewDefaultTitleBinding((LinearLayout) rootView, expandHint, icon,
          title);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
