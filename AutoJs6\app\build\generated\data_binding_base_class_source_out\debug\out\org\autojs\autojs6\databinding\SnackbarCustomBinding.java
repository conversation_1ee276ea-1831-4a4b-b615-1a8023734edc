// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.core.ui.widget.CustomSnackbarContentLayout;
import org.autojs.autojs6.R;

public final class SnackbarCustomBinding implements ViewBinding {
  @NonNull
  private final CustomSnackbarContentLayout rootView;

  @NonNull
  public final Button snackbarActionOne;

  @NonNull
  public final Button snackbarActionTwo;

  @NonNull
  public final TextView snackbarText;

  private SnackbarCustomBinding(@NonNull CustomSnackbarContentLayout rootView,
      @NonNull Button snackbarActionOne, @NonNull Button snackbarActionTwo,
      @NonNull TextView snackbarText) {
    this.rootView = rootView;
    this.snackbarActionOne = snackbarActionOne;
    this.snackbarActionTwo = snackbarActionTwo;
    this.snackbarText = snackbarText;
  }

  @Override
  @NonNull
  public CustomSnackbarContentLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SnackbarCustomBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SnackbarCustomBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.snackbar_custom, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SnackbarCustomBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.snackbar_action_one;
      Button snackbarActionOne = ViewBindings.findChildViewById(rootView, id);
      if (snackbarActionOne == null) {
        break missingId;
      }

      id = R.id.snackbar_action_two;
      Button snackbarActionTwo = ViewBindings.findChildViewById(rootView, id);
      if (snackbarActionTwo == null) {
        break missingId;
      }

      id = R.id.snackbar_text;
      TextView snackbarText = ViewBindings.findChildViewById(rootView, id);
      if (snackbarText == null) {
        break missingId;
      }

      return new SnackbarCustomBinding((CustomSnackbarContentLayout) rootView, snackbarActionOne,
          snackbarActionTwo, snackbarText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
