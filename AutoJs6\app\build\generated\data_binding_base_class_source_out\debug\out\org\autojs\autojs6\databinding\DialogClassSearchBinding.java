// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import me.zhanghai.android.materialprogressbar.MaterialProgressBar;
import org.autojs.autojs6.R;

public final class DialogClassSearchBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatEditText keywords;

  @NonNull
  public final MaterialProgressBar progressBar;

  @NonNull
  public final RecyclerView resultList;

  private DialogClassSearchBinding(@NonNull LinearLayout rootView,
      @NonNull AppCompatEditText keywords, @NonNull MaterialProgressBar progressBar,
      @NonNull RecyclerView resultList) {
    this.rootView = rootView;
    this.keywords = keywords;
    this.progressBar = progressBar;
    this.resultList = resultList;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogClassSearchBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogClassSearchBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_class_search, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogClassSearchBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.keywords;
      AppCompatEditText keywords = ViewBindings.findChildViewById(rootView, id);
      if (keywords == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      MaterialProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.result_list;
      RecyclerView resultList = ViewBindings.findChildViewById(rootView, id);
      if (resultList == null) {
        break missingId;
      }

      return new DialogClassSearchBinding((LinearLayout) rootView, keywords, progressBar,
          resultList);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
