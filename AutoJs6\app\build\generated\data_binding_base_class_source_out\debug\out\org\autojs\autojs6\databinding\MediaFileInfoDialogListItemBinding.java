// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class MediaFileInfoDialogListItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView albumColon;

  @NonNull
  public final Guideline albumGuideline;

  @NonNull
  public final TextView albumLabel;

  @NonNull
  public final ConstraintLayout albumParent;

  @NonNull
  public final TextView albumValue;

  @NonNull
  public final TextView aspectRatioColon;

  @NonNull
  public final Guideline aspectRatioGuideline;

  @NonNull
  public final TextView aspectRatioLabel;

  @NonNull
  public final ConstraintLayout aspectRatioParent;

  @NonNull
  public final TextView aspectRatioValue;

  @NonNull
  public final TextView audioFormatColon;

  @NonNull
  public final Guideline audioFormatGuideline;

  @NonNull
  public final TextView audioFormatLabel;

  @NonNull
  public final ConstraintLayout audioFormatParent;

  @NonNull
  public final TextView audioFormatValue;

  @NonNull
  public final TextView bitRateForAudioColon;

  @NonNull
  public final Guideline bitRateForAudioGuideline;

  @NonNull
  public final TextView bitRateForAudioLabel;

  @NonNull
  public final ConstraintLayout bitRateForAudioParent;

  @NonNull
  public final TextView bitRateForAudioValue;

  @NonNull
  public final TextView bitRateForVideoColon;

  @NonNull
  public final Guideline bitRateForVideoGuideline;

  @NonNull
  public final TextView bitRateForVideoLabel;

  @NonNull
  public final ConstraintLayout bitRateForVideoParent;

  @NonNull
  public final TextView bitRateForVideoValue;

  @NonNull
  public final TextView containerFormatColon;

  @NonNull
  public final Guideline containerFormatGuideline;

  @NonNull
  public final TextView containerFormatLabel;

  @NonNull
  public final ConstraintLayout containerFormatParent;

  @NonNull
  public final TextView containerFormatValue;

  @NonNull
  public final TextView durationColon;

  @NonNull
  public final Guideline durationGuideline;

  @NonNull
  public final TextView durationLabel;

  @NonNull
  public final ConstraintLayout durationParent;

  @NonNull
  public final TextView durationValue;

  @NonNull
  public final TextView fileSizeColon;

  @NonNull
  public final Guideline fileSizeGuideline;

  @NonNull
  public final TextView fileSizeLabel;

  @NonNull
  public final ConstraintLayout fileSizeParent;

  @NonNull
  public final TextView fileSizeValue;

  @NonNull
  public final TextView frameRateColon;

  @NonNull
  public final Guideline frameRateGuideline;

  @NonNull
  public final TextView frameRateLabel;

  @NonNull
  public final ConstraintLayout frameRateParent;

  @NonNull
  public final TextView frameRateValue;

  @NonNull
  public final TextView performerColon;

  @NonNull
  public final Guideline performerGuideline;

  @NonNull
  public final TextView performerLabel;

  @NonNull
  public final ConstraintLayout performerParent;

  @NonNull
  public final TextView performerValue;

  @NonNull
  public final TextView resolutionColon;

  @NonNull
  public final Guideline resolutionGuideline;

  @NonNull
  public final TextView resolutionLabel;

  @NonNull
  public final ConstraintLayout resolutionParent;

  @NonNull
  public final TextView resolutionValue;

  @NonNull
  public final View splitLine;

  @NonNull
  public final TextView trackNameColon;

  @NonNull
  public final Guideline trackNameGuideline;

  @NonNull
  public final TextView trackNameLabel;

  @NonNull
  public final ConstraintLayout trackNameParent;

  @NonNull
  public final TextView trackNameValue;

  @NonNull
  public final TextView videoFormatColon;

  @NonNull
  public final Guideline videoFormatGuideline;

  @NonNull
  public final TextView videoFormatLabel;

  @NonNull
  public final ConstraintLayout videoFormatParent;

  @NonNull
  public final TextView videoFormatValue;

  private MediaFileInfoDialogListItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView albumColon, @NonNull Guideline albumGuideline, @NonNull TextView albumLabel,
      @NonNull ConstraintLayout albumParent, @NonNull TextView albumValue,
      @NonNull TextView aspectRatioColon, @NonNull Guideline aspectRatioGuideline,
      @NonNull TextView aspectRatioLabel, @NonNull ConstraintLayout aspectRatioParent,
      @NonNull TextView aspectRatioValue, @NonNull TextView audioFormatColon,
      @NonNull Guideline audioFormatGuideline, @NonNull TextView audioFormatLabel,
      @NonNull ConstraintLayout audioFormatParent, @NonNull TextView audioFormatValue,
      @NonNull TextView bitRateForAudioColon, @NonNull Guideline bitRateForAudioGuideline,
      @NonNull TextView bitRateForAudioLabel, @NonNull ConstraintLayout bitRateForAudioParent,
      @NonNull TextView bitRateForAudioValue, @NonNull TextView bitRateForVideoColon,
      @NonNull Guideline bitRateForVideoGuideline, @NonNull TextView bitRateForVideoLabel,
      @NonNull ConstraintLayout bitRateForVideoParent, @NonNull TextView bitRateForVideoValue,
      @NonNull TextView containerFormatColon, @NonNull Guideline containerFormatGuideline,
      @NonNull TextView containerFormatLabel, @NonNull ConstraintLayout containerFormatParent,
      @NonNull TextView containerFormatValue, @NonNull TextView durationColon,
      @NonNull Guideline durationGuideline, @NonNull TextView durationLabel,
      @NonNull ConstraintLayout durationParent, @NonNull TextView durationValue,
      @NonNull TextView fileSizeColon, @NonNull Guideline fileSizeGuideline,
      @NonNull TextView fileSizeLabel, @NonNull ConstraintLayout fileSizeParent,
      @NonNull TextView fileSizeValue, @NonNull TextView frameRateColon,
      @NonNull Guideline frameRateGuideline, @NonNull TextView frameRateLabel,
      @NonNull ConstraintLayout frameRateParent, @NonNull TextView frameRateValue,
      @NonNull TextView performerColon, @NonNull Guideline performerGuideline,
      @NonNull TextView performerLabel, @NonNull ConstraintLayout performerParent,
      @NonNull TextView performerValue, @NonNull TextView resolutionColon,
      @NonNull Guideline resolutionGuideline, @NonNull TextView resolutionLabel,
      @NonNull ConstraintLayout resolutionParent, @NonNull TextView resolutionValue,
      @NonNull View splitLine, @NonNull TextView trackNameColon,
      @NonNull Guideline trackNameGuideline, @NonNull TextView trackNameLabel,
      @NonNull ConstraintLayout trackNameParent, @NonNull TextView trackNameValue,
      @NonNull TextView videoFormatColon, @NonNull Guideline videoFormatGuideline,
      @NonNull TextView videoFormatLabel, @NonNull ConstraintLayout videoFormatParent,
      @NonNull TextView videoFormatValue) {
    this.rootView = rootView;
    this.albumColon = albumColon;
    this.albumGuideline = albumGuideline;
    this.albumLabel = albumLabel;
    this.albumParent = albumParent;
    this.albumValue = albumValue;
    this.aspectRatioColon = aspectRatioColon;
    this.aspectRatioGuideline = aspectRatioGuideline;
    this.aspectRatioLabel = aspectRatioLabel;
    this.aspectRatioParent = aspectRatioParent;
    this.aspectRatioValue = aspectRatioValue;
    this.audioFormatColon = audioFormatColon;
    this.audioFormatGuideline = audioFormatGuideline;
    this.audioFormatLabel = audioFormatLabel;
    this.audioFormatParent = audioFormatParent;
    this.audioFormatValue = audioFormatValue;
    this.bitRateForAudioColon = bitRateForAudioColon;
    this.bitRateForAudioGuideline = bitRateForAudioGuideline;
    this.bitRateForAudioLabel = bitRateForAudioLabel;
    this.bitRateForAudioParent = bitRateForAudioParent;
    this.bitRateForAudioValue = bitRateForAudioValue;
    this.bitRateForVideoColon = bitRateForVideoColon;
    this.bitRateForVideoGuideline = bitRateForVideoGuideline;
    this.bitRateForVideoLabel = bitRateForVideoLabel;
    this.bitRateForVideoParent = bitRateForVideoParent;
    this.bitRateForVideoValue = bitRateForVideoValue;
    this.containerFormatColon = containerFormatColon;
    this.containerFormatGuideline = containerFormatGuideline;
    this.containerFormatLabel = containerFormatLabel;
    this.containerFormatParent = containerFormatParent;
    this.containerFormatValue = containerFormatValue;
    this.durationColon = durationColon;
    this.durationGuideline = durationGuideline;
    this.durationLabel = durationLabel;
    this.durationParent = durationParent;
    this.durationValue = durationValue;
    this.fileSizeColon = fileSizeColon;
    this.fileSizeGuideline = fileSizeGuideline;
    this.fileSizeLabel = fileSizeLabel;
    this.fileSizeParent = fileSizeParent;
    this.fileSizeValue = fileSizeValue;
    this.frameRateColon = frameRateColon;
    this.frameRateGuideline = frameRateGuideline;
    this.frameRateLabel = frameRateLabel;
    this.frameRateParent = frameRateParent;
    this.frameRateValue = frameRateValue;
    this.performerColon = performerColon;
    this.performerGuideline = performerGuideline;
    this.performerLabel = performerLabel;
    this.performerParent = performerParent;
    this.performerValue = performerValue;
    this.resolutionColon = resolutionColon;
    this.resolutionGuideline = resolutionGuideline;
    this.resolutionLabel = resolutionLabel;
    this.resolutionParent = resolutionParent;
    this.resolutionValue = resolutionValue;
    this.splitLine = splitLine;
    this.trackNameColon = trackNameColon;
    this.trackNameGuideline = trackNameGuideline;
    this.trackNameLabel = trackNameLabel;
    this.trackNameParent = trackNameParent;
    this.trackNameValue = trackNameValue;
    this.videoFormatColon = videoFormatColon;
    this.videoFormatGuideline = videoFormatGuideline;
    this.videoFormatLabel = videoFormatLabel;
    this.videoFormatParent = videoFormatParent;
    this.videoFormatValue = videoFormatValue;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MediaFileInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MediaFileInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.media_file_info_dialog_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MediaFileInfoDialogListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.album_colon;
      TextView albumColon = ViewBindings.findChildViewById(rootView, id);
      if (albumColon == null) {
        break missingId;
      }

      id = R.id.album_guideline;
      Guideline albumGuideline = ViewBindings.findChildViewById(rootView, id);
      if (albumGuideline == null) {
        break missingId;
      }

      id = R.id.album_label;
      TextView albumLabel = ViewBindings.findChildViewById(rootView, id);
      if (albumLabel == null) {
        break missingId;
      }

      id = R.id.album_parent;
      ConstraintLayout albumParent = ViewBindings.findChildViewById(rootView, id);
      if (albumParent == null) {
        break missingId;
      }

      id = R.id.album_value;
      TextView albumValue = ViewBindings.findChildViewById(rootView, id);
      if (albumValue == null) {
        break missingId;
      }

      id = R.id.aspect_ratio_colon;
      TextView aspectRatioColon = ViewBindings.findChildViewById(rootView, id);
      if (aspectRatioColon == null) {
        break missingId;
      }

      id = R.id.aspect_ratio_guideline;
      Guideline aspectRatioGuideline = ViewBindings.findChildViewById(rootView, id);
      if (aspectRatioGuideline == null) {
        break missingId;
      }

      id = R.id.aspect_ratio_label;
      TextView aspectRatioLabel = ViewBindings.findChildViewById(rootView, id);
      if (aspectRatioLabel == null) {
        break missingId;
      }

      id = R.id.aspect_ratio_parent;
      ConstraintLayout aspectRatioParent = ViewBindings.findChildViewById(rootView, id);
      if (aspectRatioParent == null) {
        break missingId;
      }

      id = R.id.aspect_ratio_value;
      TextView aspectRatioValue = ViewBindings.findChildViewById(rootView, id);
      if (aspectRatioValue == null) {
        break missingId;
      }

      id = R.id.audio_format_colon;
      TextView audioFormatColon = ViewBindings.findChildViewById(rootView, id);
      if (audioFormatColon == null) {
        break missingId;
      }

      id = R.id.audio_format_guideline;
      Guideline audioFormatGuideline = ViewBindings.findChildViewById(rootView, id);
      if (audioFormatGuideline == null) {
        break missingId;
      }

      id = R.id.audio_format_label;
      TextView audioFormatLabel = ViewBindings.findChildViewById(rootView, id);
      if (audioFormatLabel == null) {
        break missingId;
      }

      id = R.id.audio_format_parent;
      ConstraintLayout audioFormatParent = ViewBindings.findChildViewById(rootView, id);
      if (audioFormatParent == null) {
        break missingId;
      }

      id = R.id.audio_format_value;
      TextView audioFormatValue = ViewBindings.findChildViewById(rootView, id);
      if (audioFormatValue == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_audio_colon;
      TextView bitRateForAudioColon = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForAudioColon == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_audio_guideline;
      Guideline bitRateForAudioGuideline = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForAudioGuideline == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_audio_label;
      TextView bitRateForAudioLabel = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForAudioLabel == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_audio_parent;
      ConstraintLayout bitRateForAudioParent = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForAudioParent == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_audio_value;
      TextView bitRateForAudioValue = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForAudioValue == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_video_colon;
      TextView bitRateForVideoColon = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForVideoColon == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_video_guideline;
      Guideline bitRateForVideoGuideline = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForVideoGuideline == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_video_label;
      TextView bitRateForVideoLabel = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForVideoLabel == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_video_parent;
      ConstraintLayout bitRateForVideoParent = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForVideoParent == null) {
        break missingId;
      }

      id = R.id.bit_rate_for_video_value;
      TextView bitRateForVideoValue = ViewBindings.findChildViewById(rootView, id);
      if (bitRateForVideoValue == null) {
        break missingId;
      }

      id = R.id.container_format_colon;
      TextView containerFormatColon = ViewBindings.findChildViewById(rootView, id);
      if (containerFormatColon == null) {
        break missingId;
      }

      id = R.id.container_format_guideline;
      Guideline containerFormatGuideline = ViewBindings.findChildViewById(rootView, id);
      if (containerFormatGuideline == null) {
        break missingId;
      }

      id = R.id.container_format_label;
      TextView containerFormatLabel = ViewBindings.findChildViewById(rootView, id);
      if (containerFormatLabel == null) {
        break missingId;
      }

      id = R.id.container_format_parent;
      ConstraintLayout containerFormatParent = ViewBindings.findChildViewById(rootView, id);
      if (containerFormatParent == null) {
        break missingId;
      }

      id = R.id.container_format_value;
      TextView containerFormatValue = ViewBindings.findChildViewById(rootView, id);
      if (containerFormatValue == null) {
        break missingId;
      }

      id = R.id.duration_colon;
      TextView durationColon = ViewBindings.findChildViewById(rootView, id);
      if (durationColon == null) {
        break missingId;
      }

      id = R.id.duration_guideline;
      Guideline durationGuideline = ViewBindings.findChildViewById(rootView, id);
      if (durationGuideline == null) {
        break missingId;
      }

      id = R.id.duration_label;
      TextView durationLabel = ViewBindings.findChildViewById(rootView, id);
      if (durationLabel == null) {
        break missingId;
      }

      id = R.id.duration_parent;
      ConstraintLayout durationParent = ViewBindings.findChildViewById(rootView, id);
      if (durationParent == null) {
        break missingId;
      }

      id = R.id.duration_value;
      TextView durationValue = ViewBindings.findChildViewById(rootView, id);
      if (durationValue == null) {
        break missingId;
      }

      id = R.id.file_size_colon;
      TextView fileSizeColon = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeColon == null) {
        break missingId;
      }

      id = R.id.file_size_guideline;
      Guideline fileSizeGuideline = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeGuideline == null) {
        break missingId;
      }

      id = R.id.file_size_label;
      TextView fileSizeLabel = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeLabel == null) {
        break missingId;
      }

      id = R.id.file_size_parent;
      ConstraintLayout fileSizeParent = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeParent == null) {
        break missingId;
      }

      id = R.id.file_size_value;
      TextView fileSizeValue = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeValue == null) {
        break missingId;
      }

      id = R.id.frame_rate_colon;
      TextView frameRateColon = ViewBindings.findChildViewById(rootView, id);
      if (frameRateColon == null) {
        break missingId;
      }

      id = R.id.frame_rate_guideline;
      Guideline frameRateGuideline = ViewBindings.findChildViewById(rootView, id);
      if (frameRateGuideline == null) {
        break missingId;
      }

      id = R.id.frame_rate_label;
      TextView frameRateLabel = ViewBindings.findChildViewById(rootView, id);
      if (frameRateLabel == null) {
        break missingId;
      }

      id = R.id.frame_rate_parent;
      ConstraintLayout frameRateParent = ViewBindings.findChildViewById(rootView, id);
      if (frameRateParent == null) {
        break missingId;
      }

      id = R.id.frame_rate_value;
      TextView frameRateValue = ViewBindings.findChildViewById(rootView, id);
      if (frameRateValue == null) {
        break missingId;
      }

      id = R.id.performer_colon;
      TextView performerColon = ViewBindings.findChildViewById(rootView, id);
      if (performerColon == null) {
        break missingId;
      }

      id = R.id.performer_guideline;
      Guideline performerGuideline = ViewBindings.findChildViewById(rootView, id);
      if (performerGuideline == null) {
        break missingId;
      }

      id = R.id.performer_label;
      TextView performerLabel = ViewBindings.findChildViewById(rootView, id);
      if (performerLabel == null) {
        break missingId;
      }

      id = R.id.performer_parent;
      ConstraintLayout performerParent = ViewBindings.findChildViewById(rootView, id);
      if (performerParent == null) {
        break missingId;
      }

      id = R.id.performer_value;
      TextView performerValue = ViewBindings.findChildViewById(rootView, id);
      if (performerValue == null) {
        break missingId;
      }

      id = R.id.resolution_colon;
      TextView resolutionColon = ViewBindings.findChildViewById(rootView, id);
      if (resolutionColon == null) {
        break missingId;
      }

      id = R.id.resolution_guideline;
      Guideline resolutionGuideline = ViewBindings.findChildViewById(rootView, id);
      if (resolutionGuideline == null) {
        break missingId;
      }

      id = R.id.resolution_label;
      TextView resolutionLabel = ViewBindings.findChildViewById(rootView, id);
      if (resolutionLabel == null) {
        break missingId;
      }

      id = R.id.resolution_parent;
      ConstraintLayout resolutionParent = ViewBindings.findChildViewById(rootView, id);
      if (resolutionParent == null) {
        break missingId;
      }

      id = R.id.resolution_value;
      TextView resolutionValue = ViewBindings.findChildViewById(rootView, id);
      if (resolutionValue == null) {
        break missingId;
      }

      id = R.id.split_line;
      View splitLine = ViewBindings.findChildViewById(rootView, id);
      if (splitLine == null) {
        break missingId;
      }

      id = R.id.track_name_colon;
      TextView trackNameColon = ViewBindings.findChildViewById(rootView, id);
      if (trackNameColon == null) {
        break missingId;
      }

      id = R.id.track_name_guideline;
      Guideline trackNameGuideline = ViewBindings.findChildViewById(rootView, id);
      if (trackNameGuideline == null) {
        break missingId;
      }

      id = R.id.track_name_label;
      TextView trackNameLabel = ViewBindings.findChildViewById(rootView, id);
      if (trackNameLabel == null) {
        break missingId;
      }

      id = R.id.track_name_parent;
      ConstraintLayout trackNameParent = ViewBindings.findChildViewById(rootView, id);
      if (trackNameParent == null) {
        break missingId;
      }

      id = R.id.track_name_value;
      TextView trackNameValue = ViewBindings.findChildViewById(rootView, id);
      if (trackNameValue == null) {
        break missingId;
      }

      id = R.id.video_format_colon;
      TextView videoFormatColon = ViewBindings.findChildViewById(rootView, id);
      if (videoFormatColon == null) {
        break missingId;
      }

      id = R.id.video_format_guideline;
      Guideline videoFormatGuideline = ViewBindings.findChildViewById(rootView, id);
      if (videoFormatGuideline == null) {
        break missingId;
      }

      id = R.id.video_format_label;
      TextView videoFormatLabel = ViewBindings.findChildViewById(rootView, id);
      if (videoFormatLabel == null) {
        break missingId;
      }

      id = R.id.video_format_parent;
      ConstraintLayout videoFormatParent = ViewBindings.findChildViewById(rootView, id);
      if (videoFormatParent == null) {
        break missingId;
      }

      id = R.id.video_format_value;
      TextView videoFormatValue = ViewBindings.findChildViewById(rootView, id);
      if (videoFormatValue == null) {
        break missingId;
      }

      return new MediaFileInfoDialogListItemBinding((ConstraintLayout) rootView, albumColon,
          albumGuideline, albumLabel, albumParent, albumValue, aspectRatioColon,
          aspectRatioGuideline, aspectRatioLabel, aspectRatioParent, aspectRatioValue,
          audioFormatColon, audioFormatGuideline, audioFormatLabel, audioFormatParent,
          audioFormatValue, bitRateForAudioColon, bitRateForAudioGuideline, bitRateForAudioLabel,
          bitRateForAudioParent, bitRateForAudioValue, bitRateForVideoColon,
          bitRateForVideoGuideline, bitRateForVideoLabel, bitRateForVideoParent,
          bitRateForVideoValue, containerFormatColon, containerFormatGuideline,
          containerFormatLabel, containerFormatParent, containerFormatValue, durationColon,
          durationGuideline, durationLabel, durationParent, durationValue, fileSizeColon,
          fileSizeGuideline, fileSizeLabel, fileSizeParent, fileSizeValue, frameRateColon,
          frameRateGuideline, frameRateLabel, frameRateParent, frameRateValue, performerColon,
          performerGuideline, performerLabel, performerParent, performerValue, resolutionColon,
          resolutionGuideline, resolutionLabel, resolutionParent, resolutionValue, splitLine,
          trackNameColon, trackNameGuideline, trackNameLabel, trackNameParent, trackNameValue,
          videoFormatColon, videoFormatGuideline, videoFormatLabel, videoFormatParent,
          videoFormatValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
