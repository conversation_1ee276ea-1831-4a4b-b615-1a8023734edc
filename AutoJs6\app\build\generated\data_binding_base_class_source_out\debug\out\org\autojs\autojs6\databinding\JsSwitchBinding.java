// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.core.ui.widget.JsSwitch;
import org.autojs.autojs6.R;

public final class JsSwitchBinding implements ViewBinding {
  @NonNull
  private final JsSwitch rootView;

  @NonNull
  public final JsSwitch jsSwitch;

  private JsSwitchBinding(@NonNull JsSwitch rootView, @NonNull JsSwitch jsSwitch) {
    this.rootView = rootView;
    this.jsSwitch = jsSwitch;
  }

  @Override
  @NonNull
  public JsSwitch getRoot() {
    return rootView;
  }

  @NonNull
  public static JsSwitchBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static JsSwitchBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.js_switch, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static JsSwitchBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    JsSwitch jsSwitch = (JsSwitch) rootView;

    return new JsSwitchBinding((JsSwitch) rootView, jsSwitch);
  }
}
