// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class TaskListRecyclerViewItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayoutCompat rootView;

  @NonNull
  public final LinearLayoutCompat item;

  @NonNull
  public final TextView name;

  @NonNull
  public final ImageView stop;

  @NonNull
  public final TextView taskListFilePath;

  private TaskListRecyclerViewItemBinding(@NonNull LinearLayoutCompat rootView,
      @NonNull LinearLayoutCompat item, @NonNull TextView name, @NonNull ImageView stop,
      @NonNull TextView taskListFilePath) {
    this.rootView = rootView;
    this.item = item;
    this.name = name;
    this.stop = stop;
    this.taskListFilePath = taskListFilePath;
  }

  @Override
  @NonNull
  public LinearLayoutCompat getRoot() {
    return rootView;
  }

  @NonNull
  public static TaskListRecyclerViewItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static TaskListRecyclerViewItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.task_list_recycler_view_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static TaskListRecyclerViewItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayoutCompat item = (LinearLayoutCompat) rootView;

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.stop;
      ImageView stop = ViewBindings.findChildViewById(rootView, id);
      if (stop == null) {
        break missingId;
      }

      id = R.id.task_list_file_path;
      TextView taskListFilePath = ViewBindings.findChildViewById(rootView, id);
      if (taskListFilePath == null) {
        break missingId;
      }

      return new TaskListRecyclerViewItemBinding((LinearLayoutCompat) rootView, item, name, stop,
          taskListFilePath);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
