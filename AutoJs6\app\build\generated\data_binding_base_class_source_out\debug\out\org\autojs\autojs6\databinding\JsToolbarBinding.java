// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.core.ui.widget.JsToolbar;
import org.autojs.autojs6.R;

public final class JsToolbarBinding implements ViewBinding {
  @NonNull
  private final JsToolbar rootView;

  @NonNull
  public final JsToolbar toolbar;

  private JsToolbarBinding(@NonNull JsToolbar rootView, @NonNull JsToolbar toolbar) {
    this.rootView = rootView;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public JsToolbar getRoot() {
    return rootView;
  }

  @NonNull
  public static JsToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static JsToolbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.js_toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static JsToolbarBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    JsToolbar toolbar = (JsToolbar) rootView;

    return new JsToolbarBinding((JsToolbar) rootView, toolbar);
  }
}
