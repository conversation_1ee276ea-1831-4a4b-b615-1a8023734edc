package com.bm.atool;

import android.content.Context;
import android.util.Log;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.bm.atool.autojs.AutoJsFramework;
import com.bm.autojs6.module.AutoJs6Module;
import com.bm.autojs6.module.model.Script;
import com.bm.autojs6.module.model.ScriptResult;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * AutoJs6 功能测试
 */
@RunWith(AndroidJUnit4.class)
public class AutoJs6Test {
    private static final String TAG = "AutoJs6Test";
    
    private Context context;
    private AutoJs6Module autoJs6Module;
    private AutoJsFramework autoJsFramework;
    
    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        
        // 初始化AutoJs6Module
        autoJs6Module = AutoJs6Module.getInstance();
        autoJs6Module.initialize(context);
        
        // 初始化AutoJsFramework
        autoJsFramework = AutoJsFramework.getInstance();
        autoJsFramework.initialize((android.app.Application) context.getApplicationContext());
        
        Log.d(TAG, "Test setup completed");
    }
    
    @Test
    public void testAutoJs6ModuleInitialization() {
        Log.d(TAG, "Testing AutoJs6Module initialization");
        
        assertTrue("AutoJs6Module should be initialized", autoJs6Module.isInitialized());
        assertEquals("Running script count should be 0", 0, autoJs6Module.getRunningScriptCount());
        
        Log.d(TAG, "AutoJs6Module initialization test passed");
    }
    
    @Test
    public void testAutoJsFrameworkInitialization() {
        Log.d(TAG, "Testing AutoJsFramework initialization");
        
        assertTrue("AutoJsFramework should be initialized", autoJsFramework.isInitialized());
        assertEquals("Running script count should be 0", 0, autoJsFramework.getRunningScriptCount());
        
        Log.d(TAG, "AutoJsFramework initialization test passed");
    }
    
    @Test
    public void testSimpleScriptExecution() {
        Log.d(TAG, "Testing simple script execution");
        
        String scriptContent = "console.log('Hello AutoJs6!'); 'Test completed successfully';";
        Script script = new Script("SimpleTest", scriptContent);
        
        ScriptResult result = autoJs6Module.executeScript(script);
        
        assertNotNull("Script result should not be null", result);
        assertTrue("Script execution should be successful", result.isSuccess());
        assertNotNull("Script result should have content", result.getResult());
        
        Log.d(TAG, "Simple script execution test passed: " + result.getResult());
    }
    
    @Test
    public void testScriptWithVariables() {
        Log.d(TAG, "Testing script with variables");
        
        String scriptContent = 
            "var a = 10; " +
            "var b = 20; " +
            "var result = a + b; " +
            "console.log('Result: ' + result); " +
            "'Calculation result: ' + result;";
        
        Script script = new Script("VariableTest", scriptContent);
        ScriptResult result = autoJs6Module.executeScript(script);
        
        assertNotNull("Script result should not be null", result);
        assertTrue("Script execution should be successful", result.isSuccess());
        assertTrue("Result should contain calculation", result.getResult().contains("30"));
        
        Log.d(TAG, "Script with variables test passed: " + result.getResult());
    }
    
    @Test
    public void testScriptWithFunctions() {
        Log.d(TAG, "Testing script with functions");
        
        String scriptContent = 
            "function greet(name) { " +
            "  return 'Hello, ' + name + '!'; " +
            "} " +
            "var greeting = greet('AutoJs6'); " +
            "console.log(greeting); " +
            "greeting;";
        
        Script script = new Script("FunctionTest", scriptContent);
        ScriptResult result = autoJs6Module.executeScript(script);
        
        assertNotNull("Script result should not be null", result);
        assertTrue("Script execution should be successful", result.isSuccess());
        assertTrue("Result should contain greeting", result.getResult().contains("Hello, AutoJs6!"));
        
        Log.d(TAG, "Script with functions test passed: " + result.getResult());
    }
    
    @Test
    public void testScriptWithUtils() {
        Log.d(TAG, "Testing script with utils");
        
        String scriptContent = 
            "if (typeof utils !== 'undefined') { " +
            "  utils.log('Utils is available'); " +
            "  'Utils test passed'; " +
            "} else { " +
            "  'Utils not available'; " +
            "}";
        
        Script script = new Script("UtilsTest", scriptContent);
        ScriptResult result = autoJs6Module.executeScript(script);
        
        assertNotNull("Script result should not be null", result);
        assertTrue("Script execution should be successful", result.isSuccess());
        assertTrue("Utils should be available", result.getResult().contains("Utils test passed"));
        
        Log.d(TAG, "Script with utils test passed: " + result.getResult());
    }
    
    @Test
    public void testAsyncScriptExecution() throws InterruptedException {
        Log.d(TAG, "Testing async script execution");
        
        String scriptContent = "console.log('Async test'); 'Async execution completed';";
        Script script = new Script("AsyncTest", scriptContent);
        
        CountDownLatch latch = new CountDownLatch(1);
        final ScriptResult[] resultHolder = new ScriptResult[1];
        
        autoJs6Module.executeScriptAsync(script)
            .thenAccept(result -> {
                resultHolder[0] = result;
                latch.countDown();
            })
            .exceptionally(throwable -> {
                Log.e(TAG, "Async execution failed", throwable);
                latch.countDown();
                return null;
            });
        
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue("Async execution should complete within timeout", completed);
        
        ScriptResult result = resultHolder[0];
        assertNotNull("Async script result should not be null", result);
        assertTrue("Async script execution should be successful", result.isSuccess());
        
        Log.d(TAG, "Async script execution test passed: " + result.getResult());
    }
    
    @Test
    public void testFrameworkScriptExecution() throws InterruptedException {
        Log.d(TAG, "Testing framework script execution");
        
        String scriptContent = "console.log('Framework test'); 'Framework execution completed';";
        
        CountDownLatch latch = new CountDownLatch(1);
        final String[] resultHolder = new String[1];
        final boolean[] successHolder = new boolean[1];
        
        String executionId = autoJsFramework.executeScript(scriptContent, "FrameworkTest", 
            new AutoJsFramework.ScriptExecutionCallback() {
                @Override
                public void onStart(String scriptName) {
                    Log.d(TAG, "Framework script started: " + scriptName);
                }
                
                @Override
                public void onSuccess(String scriptName, String result) {
                    Log.d(TAG, "Framework script completed: " + scriptName + ", result: " + result);
                    resultHolder[0] = result;
                    successHolder[0] = true;
                    latch.countDown();
                }
                
                @Override
                public void onError(String scriptName, String error) {
                    Log.e(TAG, "Framework script failed: " + scriptName + ", error: " + error);
                    resultHolder[0] = error;
                    successHolder[0] = false;
                    latch.countDown();
                }
            });
        
        assertNotNull("Execution ID should not be null", executionId);
        
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue("Framework execution should complete within timeout", completed);
        assertTrue("Framework script execution should be successful", successHolder[0]);
        assertNotNull("Framework script result should not be null", resultHolder[0]);
        
        Log.d(TAG, "Framework script execution test passed: " + resultHolder[0]);
    }
    
    @Test
    public void testScriptSyntaxError() {
        Log.d(TAG, "Testing script syntax error handling");
        
        String scriptContent = "var a = 10; var b = ; console.log(a + b);"; // 语法错误
        Script script = new Script("SyntaxErrorTest", scriptContent);
        
        ScriptResult result = autoJs6Module.executeScript(script);
        
        assertNotNull("Script result should not be null", result);
        assertFalse("Script execution should fail due to syntax error", result.isSuccess());
        assertNotNull("Error message should be provided", result.getError());
        
        Log.d(TAG, "Script syntax error test passed: " + result.getError());
    }
    
    @Test
    public void testStopAllScripts() {
        Log.d(TAG, "Testing stop all scripts");
        
        // 执行一个脚本
        String scriptContent = "console.log('Test script'); 'Script completed';";
        Script script = new Script("StopTest", scriptContent);
        autoJs6Module.executeScript(script);
        
        // 停止所有脚本
        autoJs6Module.stopAllScripts();
        
        // 验证脚本数量
        assertEquals("Running script count should be 0 after stop", 0, autoJs6Module.getRunningScriptCount());
        
        Log.d(TAG, "Stop all scripts test passed");
    }
}
