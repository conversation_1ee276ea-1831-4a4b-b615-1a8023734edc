// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.core.ui.widget.JsTabLayout;
import org.autojs.autojs6.R;

public final class JsTablayoutBinding implements ViewBinding {
  @NonNull
  private final JsTabLayout rootView;

  private JsTablayoutBinding(@NonNull JsTabLayout rootView) {
    this.rootView = rootView;
  }

  @Override
  @NonNull
  public JsTabLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static JsTablayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static JsTablayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.js_tablayout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static JsTablayoutBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    return new JsTablayoutBinding((JsTabLayout) rootView);
  }
}
