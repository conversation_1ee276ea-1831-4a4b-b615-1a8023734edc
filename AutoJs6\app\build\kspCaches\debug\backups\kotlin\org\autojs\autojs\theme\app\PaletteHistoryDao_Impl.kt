package org.autojs.autojs.theme.app

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performInTransactionSuspending
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import javax.`annotation`.processing.Generated
import kotlin.Boolean
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class PaletteHistoryDao_Impl(
  __db: RoomDatabase,
) : PaletteHistoryDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfPaletteHistory: EntityInsertAdapter<ColorEntities.PaletteHistory>

  private val __deleteAdapterOfPaletteHistory:
      EntityDeleteOrUpdateAdapter<ColorEntities.PaletteHistory>

  private val __updateAdapterOfPaletteHistory:
      EntityDeleteOrUpdateAdapter<ColorEntities.PaletteHistory>
  init {
    this.__db = __db
    this.__insertAdapterOfPaletteHistory = object :
        EntityInsertAdapter<ColorEntities.PaletteHistory>() {
      protected override fun createQuery(): String =
          "INSERT OR IGNORE INTO `palette_history` (`id`,`last_used_time`,`hex`) VALUES (nullif(?, 0),?,?)"

      protected override fun bind(statement: SQLiteStatement,
          entity: ColorEntities.PaletteHistory) {
        statement.bindLong(1, entity.id.toLong())
        statement.bindLong(2, entity.lastUsedTime)
        val _tmpColorInfo: ColorEntities.ColorInfo = entity.colorInfo
        statement.bindText(3, _tmpColorInfo.hex)
      }
    }
    this.__deleteAdapterOfPaletteHistory = object :
        EntityDeleteOrUpdateAdapter<ColorEntities.PaletteHistory>() {
      protected override fun createQuery(): String = "DELETE FROM `palette_history` WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement,
          entity: ColorEntities.PaletteHistory) {
        statement.bindLong(1, entity.id.toLong())
      }
    }
    this.__updateAdapterOfPaletteHistory = object :
        EntityDeleteOrUpdateAdapter<ColorEntities.PaletteHistory>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `palette_history` SET `id` = ?,`last_used_time` = ?,`hex` = ? WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement,
          entity: ColorEntities.PaletteHistory) {
        statement.bindLong(1, entity.id.toLong())
        statement.bindLong(2, entity.lastUsedTime)
        val _tmpColorInfo: ColorEntities.ColorInfo = entity.colorInfo
        statement.bindText(3, _tmpColorInfo.hex)
        statement.bindLong(4, entity.id.toLong())
      }
    }
  }

  public override suspend fun insert(history: ColorEntities.PaletteHistory): Long =
      performSuspending(__db, false, true) { _connection ->
    val _result: Long = __insertAdapterOfPaletteHistory.insertAndReturnId(_connection, history)
    _result
  }

  public override suspend fun delete(vararg histories: ColorEntities.PaletteHistory): Int =
      performSuspending(__db, false, true) { _connection ->
    var _result: Int = 0
    _result += __deleteAdapterOfPaletteHistory.handleMultiple(_connection, histories)
    _result
  }

  public override suspend fun update(history: ColorEntities.PaletteHistory): Unit =
      performSuspending(__db, false, true) { _connection ->
    __updateAdapterOfPaletteHistory.handle(_connection, history)
  }

  public override suspend fun upsert(history: ColorEntities.PaletteHistory): Long =
      performInTransactionSuspending(__db) {
    super@PaletteHistoryDao_Impl.upsert(history)
  }

  public override suspend fun getByHex(hex: String): ColorEntities.PaletteHistory? {
    val _sql: String = "SELECT * FROM palette_history WHERE hex = ? LIMIT 1"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, hex)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfLastUsedTime: Int = getColumnIndexOrThrow(_stmt, "last_used_time")
        val _columnIndexOfHex: Int = getColumnIndexOrThrow(_stmt, "hex")
        val _result: ColorEntities.PaletteHistory?
        if (_stmt.step()) {
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpLastUsedTime: Long
          _tmpLastUsedTime = _stmt.getLong(_columnIndexOfLastUsedTime)
          val _tmpColorInfo: ColorEntities.ColorInfo
          val _tmpHex: String
          _tmpHex = _stmt.getText(_columnIndexOfHex)
          _tmpColorInfo = ColorEntities.ColorInfo(_tmpHex)
          _result = ColorEntities.PaletteHistory(_tmpId,_tmpColorInfo,_tmpLastUsedTime)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getAll(): List<ColorEntities.PaletteHistory> {
    val _sql: String = "SELECT * FROM palette_history"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfLastUsedTime: Int = getColumnIndexOrThrow(_stmt, "last_used_time")
        val _columnIndexOfHex: Int = getColumnIndexOrThrow(_stmt, "hex")
        val _result: MutableList<ColorEntities.PaletteHistory> = mutableListOf()
        while (_stmt.step()) {
          val _item: ColorEntities.PaletteHistory
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpLastUsedTime: Long
          _tmpLastUsedTime = _stmt.getLong(_columnIndexOfLastUsedTime)
          val _tmpColorInfo: ColorEntities.ColorInfo
          val _tmpHex: String
          _tmpHex = _stmt.getText(_columnIndexOfHex)
          _tmpColorInfo = ColorEntities.ColorInfo(_tmpHex)
          _item = ColorEntities.PaletteHistory(_tmpId,_tmpColorInfo,_tmpLastUsedTime)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun hasData(): Boolean {
    val _sql: String = "SELECT EXISTS(SELECT 1 FROM palette_history LIMIT 1)"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _result: Boolean
        if (_stmt.step()) {
          val _tmp: Int
          _tmp = _stmt.getLong(0).toInt()
          _result = _tmp != 0
        } else {
          _result = false
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAll() {
    val _sql: String = "DELETE FROM palette_history"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
