pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://jitpack.io' }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/public' }
    }
}

rootProject.name = "android-tool"
include ':app'
include ':autojs6-module'
// include ':AutoJs6'  // 暂时禁用，等待修复兼容性问题

// 配置AutoJs6项目路径 - 指向AutoJs6/app目录
// project(':AutoJs6').projectDir = new File('AutoJs6/app')

