// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ItemKeyStoreBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView alias;

  @NonNull
  public final ImageView delete;

  @NonNull
  public final TextView filename;

  @NonNull
  public final ImageView verify;

  private ItemKeyStoreBinding(@NonNull CardView rootView, @NonNull TextView alias,
      @NonNull ImageView delete, @NonNull TextView filename, @NonNull ImageView verify) {
    this.rootView = rootView;
    this.alias = alias;
    this.delete = delete;
    this.filename = filename;
    this.verify = verify;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemKeyStoreBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemKeyStoreBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_key_store, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemKeyStoreBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.alias;
      TextView alias = ViewBindings.findChildViewById(rootView, id);
      if (alias == null) {
        break missingId;
      }

      id = R.id.delete;
      ImageView delete = ViewBindings.findChildViewById(rootView, id);
      if (delete == null) {
        break missingId;
      }

      id = R.id.filename;
      TextView filename = ViewBindings.findChildViewById(rootView, id);
      if (filename == null) {
        break missingId;
      }

      id = R.id.verify;
      ImageView verify = ViewBindings.findChildViewById(rootView, id);
      if (verify == null) {
        break missingId;
      }

      return new ItemKeyStoreBinding((CardView) rootView, alias, delete, filename, verify);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
