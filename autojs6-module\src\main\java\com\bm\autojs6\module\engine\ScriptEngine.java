package com.bm.autojs6.module.engine;

import android.content.Context;
import android.util.Log;

import com.bm.autojs6.module.model.Script;
import com.bm.autojs6.module.model.ScriptResult;

import org.mozilla.javascript.Function;
import org.mozilla.javascript.Scriptable;
import org.mozilla.javascript.ScriptableObject;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 脚本执行引擎
 * 基于Rhino JavaScript引擎
 */
public class ScriptEngine {
    private static final String TAG = "ScriptEngine";
    
    private android.content.Context androidContext;
    private ConcurrentHashMap<String, Thread> runningScripts;
    private AtomicInteger scriptCounter;
    
    public ScriptEngine(android.content.Context context) {
        this.androidContext = context;
        this.runningScripts = new ConcurrentHashMap<>();
        this.scriptCounter = new AtomicInteger(0);
        
        Log.d(TAG, "ScriptEngine initialized");
    }
    
    /**
     * 执行脚本
     */
    public ScriptResult execute(Script script) {
        String scriptId = "script_" + scriptCounter.incrementAndGet();
        Thread currentThread = Thread.currentThread();
        
        try {
            runningScripts.put(scriptId, currentThread);
            
            Log.d(TAG, "Executing script: " + script.getName());
            
            // 创建Rhino上下文
            org.mozilla.javascript.Context rhinoContext = org.mozilla.javascript.Context.enter();
            rhinoContext.setOptimizationLevel(-1); // 解释模式，支持调试
            
            // 创建作用域
            Scriptable scope = rhinoContext.initStandardObjects();
            
            // 注入Android API
            injectAndroidAPIs(rhinoContext, scope);
            
            // 执行脚本
            Object result = rhinoContext.evaluateString(scope, script.getContent(), 
                    script.getName(), 1, null);
            
            String resultString = org.mozilla.javascript.Context.toString(result);
            
            Log.d(TAG, "Script executed successfully: " + script.getName());
            return ScriptResult.success(scriptId, resultString);
            
        } catch (Exception e) {
            Log.e(TAG, "Script execution failed: " + script.getName(), e);
            return ScriptResult.error(scriptId, e.getMessage());
            
        } finally {
            runningScripts.remove(scriptId);
            org.mozilla.javascript.Context.exit();
        }
    }
    
    /**
     * 注入Android APIs到JavaScript环境
     */
    private void injectAndroidAPIs(org.mozilla.javascript.Context context, Scriptable scope) {
        try {
            // 注入console对象
            ScriptableObject.putProperty(scope, "console", new ConsoleObject());

            // 注入Android上下文（简化版）
            ScriptableObject.putProperty(scope, "androidContext", androidContext);

            // 注入常用工具函数
            String utilsScript =
                "var utils = {" +
                "  log: function(msg) { " +
                "    if (typeof console !== 'undefined' && console.log) {" +
                "      console.log(msg); " +
                "    }" +
                "  }," +
                "  sleep: function(ms) { " +
                "    try { java.lang.Thread.sleep(ms); } catch(e) { console.log('Sleep error: ' + e); }" +
                "  }," +
                "  toast: function(msg) { " +
                "    console.log('Toast: ' + msg);" +
                "  }" +
                "};";

            // 先执行utils脚本
            context.evaluateString(scope, utilsScript, "utils_init", 1, null);

            // 验证utils对象是否正确创建
            Object utilsObj = scope.get("utils", scope);
            if (utilsObj == null || utilsObj == Scriptable.NOT_FOUND) {
                Log.w(TAG, "Utils object not properly injected");
            } else {
                Log.d(TAG, "Utils object successfully injected");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error injecting Android APIs", e);
        }
    }
    
    /**
     * 停止所有脚本
     */
    public void stopAll() {
        Log.d(TAG, "Stopping all scripts");
        for (Thread thread : runningScripts.values()) {
            if (thread != null && thread.isAlive()) {
                thread.interrupt();
            }
        }
        runningScripts.clear();
    }
    
    /**
     * 获取正在运行的脚本数量
     */
    public int getRunningCount() {
        return runningScripts.size();
    }
    
    /**
     * 关闭引擎
     */
    public void shutdown() {
        stopAll();
        Log.d(TAG, "ScriptEngine shutdown");
    }
    
    /**
     * Console对象实现
     */
    private static class ConsoleObject extends ScriptableObject {
        @Override
        public String getClassName() {
            return "Console";
        }
        
        public void jsFunction_log(String message) {
            Log.d("JSConsole", message);
        }
        
        public void jsFunction_error(String message) {
            Log.e("JSConsole", message);
        }
        
        public void jsFunction_warn(String message) {
            Log.w("JSConsole", message);
        }
    }
}
