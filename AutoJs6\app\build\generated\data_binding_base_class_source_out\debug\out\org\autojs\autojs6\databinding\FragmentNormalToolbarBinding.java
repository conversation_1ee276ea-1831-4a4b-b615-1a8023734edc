// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.widget.ToolbarMenuItem;
import org.autojs.autojs6.R;

public final class FragmentNormalToolbarBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ToolbarMenuItem redo;

  @NonNull
  public final ToolbarMenuItem run;

  @NonNull
  public final ToolbarMenuItem save;

  @NonNull
  public final ToolbarMenuItem undo;

  private FragmentNormalToolbarBinding(@NonNull LinearLayout rootView,
      @NonNull ToolbarMenuItem redo, @NonNull ToolbarMenuItem run, @NonNull ToolbarMenuItem save,
      @NonNull ToolbarMenuItem undo) {
    this.rootView = rootView;
    this.redo = redo;
    this.run = run;
    this.save = save;
    this.undo = undo;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentNormalToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentNormalToolbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_normal_toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentNormalToolbarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.redo;
      ToolbarMenuItem redo = ViewBindings.findChildViewById(rootView, id);
      if (redo == null) {
        break missingId;
      }

      id = R.id.run;
      ToolbarMenuItem run = ViewBindings.findChildViewById(rootView, id);
      if (run == null) {
        break missingId;
      }

      id = R.id.save;
      ToolbarMenuItem save = ViewBindings.findChildViewById(rootView, id);
      if (save == null) {
        break missingId;
      }

      id = R.id.undo;
      ToolbarMenuItem undo = ViewBindings.findChildViewById(rootView, id);
      if (undo == null) {
        break missingId;
      }

      return new FragmentNormalToolbarBinding((LinearLayout) rootView, redo, run, save, undo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
