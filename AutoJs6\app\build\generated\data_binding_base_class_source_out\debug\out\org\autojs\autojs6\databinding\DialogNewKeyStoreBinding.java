// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorRadioButton;
import org.autojs.autojs.theme.widget.ThemeColorTextInputLayout;
import org.autojs.autojs6.R;

public final class DialogNewKeyStoreBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextInputEditText alias;

  @NonNull
  public final TextInputEditText aliasPassword;

  @NonNull
  public final ThemeColorTextInputLayout aliasPasswordTextInputLayout;

  @NonNull
  public final ThemeColorTextInputLayout aliasTextInputLayout;

  @NonNull
  public final Button cancel;

  @NonNull
  public final TextInputEditText cityOrLocality;

  @NonNull
  public final ThemeColorTextInputLayout cityOrLocalityTextInputLayout;

  @NonNull
  public final Button confirm;

  @NonNull
  public final TextInputEditText countryCode;

  @NonNull
  public final ThemeColorTextInputLayout countryCodeTextInputLayout;

  @NonNull
  public final TextInputEditText filename;

  @NonNull
  public final ThemeColorTextInputLayout filenameTextInputLayout;

  @NonNull
  public final TextInputEditText firstAndLastName;

  @NonNull
  public final ThemeColorTextInputLayout firstAndLastNameTextInputLayout;

  @NonNull
  public final SwitchMaterial moreOptions;

  @NonNull
  public final LinearLayout moreOptionsContainer;

  @NonNull
  public final TextInputEditText organization;

  @NonNull
  public final ThemeColorTextInputLayout organizationTextInputLayout;

  @NonNull
  public final TextInputEditText organizationalUnit;

  @NonNull
  public final ThemeColorTextInputLayout organizationalUnitTextInputLayout;

  @NonNull
  public final TextInputEditText password;

  @NonNull
  public final ThemeColorTextInputLayout passwordTextInputLayout;

  @NonNull
  public final AppCompatSpinner signatureAlgorithms;

  @NonNull
  public final TextInputEditText stateOrProvince;

  @NonNull
  public final ThemeColorTextInputLayout stateOrProvinceTextInputLayout;

  @NonNull
  public final TextInputEditText street;

  @NonNull
  public final ThemeColorTextInputLayout streetTextInputLayout;

  @NonNull
  public final ThemeColorRadioButton typeBks;

  @NonNull
  public final ThemeColorRadioButton typeJks;

  @NonNull
  public final RadioGroup typeRadioGroup;

  @NonNull
  public final TextInputEditText validityYears;

  @NonNull
  public final ThemeColorTextInputLayout validityYearsTextInputLayout;

  private DialogNewKeyStoreBinding(@NonNull CardView rootView, @NonNull TextInputEditText alias,
      @NonNull TextInputEditText aliasPassword,
      @NonNull ThemeColorTextInputLayout aliasPasswordTextInputLayout,
      @NonNull ThemeColorTextInputLayout aliasTextInputLayout, @NonNull Button cancel,
      @NonNull TextInputEditText cityOrLocality,
      @NonNull ThemeColorTextInputLayout cityOrLocalityTextInputLayout, @NonNull Button confirm,
      @NonNull TextInputEditText countryCode,
      @NonNull ThemeColorTextInputLayout countryCodeTextInputLayout,
      @NonNull TextInputEditText filename,
      @NonNull ThemeColorTextInputLayout filenameTextInputLayout,
      @NonNull TextInputEditText firstAndLastName,
      @NonNull ThemeColorTextInputLayout firstAndLastNameTextInputLayout,
      @NonNull SwitchMaterial moreOptions, @NonNull LinearLayout moreOptionsContainer,
      @NonNull TextInputEditText organization,
      @NonNull ThemeColorTextInputLayout organizationTextInputLayout,
      @NonNull TextInputEditText organizationalUnit,
      @NonNull ThemeColorTextInputLayout organizationalUnitTextInputLayout,
      @NonNull TextInputEditText password,
      @NonNull ThemeColorTextInputLayout passwordTextInputLayout,
      @NonNull AppCompatSpinner signatureAlgorithms, @NonNull TextInputEditText stateOrProvince,
      @NonNull ThemeColorTextInputLayout stateOrProvinceTextInputLayout,
      @NonNull TextInputEditText street, @NonNull ThemeColorTextInputLayout streetTextInputLayout,
      @NonNull ThemeColorRadioButton typeBks, @NonNull ThemeColorRadioButton typeJks,
      @NonNull RadioGroup typeRadioGroup, @NonNull TextInputEditText validityYears,
      @NonNull ThemeColorTextInputLayout validityYearsTextInputLayout) {
    this.rootView = rootView;
    this.alias = alias;
    this.aliasPassword = aliasPassword;
    this.aliasPasswordTextInputLayout = aliasPasswordTextInputLayout;
    this.aliasTextInputLayout = aliasTextInputLayout;
    this.cancel = cancel;
    this.cityOrLocality = cityOrLocality;
    this.cityOrLocalityTextInputLayout = cityOrLocalityTextInputLayout;
    this.confirm = confirm;
    this.countryCode = countryCode;
    this.countryCodeTextInputLayout = countryCodeTextInputLayout;
    this.filename = filename;
    this.filenameTextInputLayout = filenameTextInputLayout;
    this.firstAndLastName = firstAndLastName;
    this.firstAndLastNameTextInputLayout = firstAndLastNameTextInputLayout;
    this.moreOptions = moreOptions;
    this.moreOptionsContainer = moreOptionsContainer;
    this.organization = organization;
    this.organizationTextInputLayout = organizationTextInputLayout;
    this.organizationalUnit = organizationalUnit;
    this.organizationalUnitTextInputLayout = organizationalUnitTextInputLayout;
    this.password = password;
    this.passwordTextInputLayout = passwordTextInputLayout;
    this.signatureAlgorithms = signatureAlgorithms;
    this.stateOrProvince = stateOrProvince;
    this.stateOrProvinceTextInputLayout = stateOrProvinceTextInputLayout;
    this.street = street;
    this.streetTextInputLayout = streetTextInputLayout;
    this.typeBks = typeBks;
    this.typeJks = typeJks;
    this.typeRadioGroup = typeRadioGroup;
    this.validityYears = validityYears;
    this.validityYearsTextInputLayout = validityYearsTextInputLayout;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogNewKeyStoreBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogNewKeyStoreBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_new_key_store, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogNewKeyStoreBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.alias;
      TextInputEditText alias = ViewBindings.findChildViewById(rootView, id);
      if (alias == null) {
        break missingId;
      }

      id = R.id.alias_password;
      TextInputEditText aliasPassword = ViewBindings.findChildViewById(rootView, id);
      if (aliasPassword == null) {
        break missingId;
      }

      id = R.id.alias_password_text_input_layout;
      ThemeColorTextInputLayout aliasPasswordTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (aliasPasswordTextInputLayout == null) {
        break missingId;
      }

      id = R.id.alias_text_input_layout;
      ThemeColorTextInputLayout aliasTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (aliasTextInputLayout == null) {
        break missingId;
      }

      id = R.id.cancel;
      Button cancel = ViewBindings.findChildViewById(rootView, id);
      if (cancel == null) {
        break missingId;
      }

      id = R.id.city_or_locality;
      TextInputEditText cityOrLocality = ViewBindings.findChildViewById(rootView, id);
      if (cityOrLocality == null) {
        break missingId;
      }

      id = R.id.city_or_locality_text_input_layout;
      ThemeColorTextInputLayout cityOrLocalityTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (cityOrLocalityTextInputLayout == null) {
        break missingId;
      }

      id = R.id.confirm;
      Button confirm = ViewBindings.findChildViewById(rootView, id);
      if (confirm == null) {
        break missingId;
      }

      id = R.id.country_code;
      TextInputEditText countryCode = ViewBindings.findChildViewById(rootView, id);
      if (countryCode == null) {
        break missingId;
      }

      id = R.id.country_code_text_input_layout;
      ThemeColorTextInputLayout countryCodeTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (countryCodeTextInputLayout == null) {
        break missingId;
      }

      id = R.id.filename;
      TextInputEditText filename = ViewBindings.findChildViewById(rootView, id);
      if (filename == null) {
        break missingId;
      }

      id = R.id.filename_text_input_layout;
      ThemeColorTextInputLayout filenameTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (filenameTextInputLayout == null) {
        break missingId;
      }

      id = R.id.first_and_last_name;
      TextInputEditText firstAndLastName = ViewBindings.findChildViewById(rootView, id);
      if (firstAndLastName == null) {
        break missingId;
      }

      id = R.id.first_and_last_name_text_input_layout;
      ThemeColorTextInputLayout firstAndLastNameTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (firstAndLastNameTextInputLayout == null) {
        break missingId;
      }

      id = R.id.more_options;
      SwitchMaterial moreOptions = ViewBindings.findChildViewById(rootView, id);
      if (moreOptions == null) {
        break missingId;
      }

      id = R.id.more_options_container;
      LinearLayout moreOptionsContainer = ViewBindings.findChildViewById(rootView, id);
      if (moreOptionsContainer == null) {
        break missingId;
      }

      id = R.id.organization;
      TextInputEditText organization = ViewBindings.findChildViewById(rootView, id);
      if (organization == null) {
        break missingId;
      }

      id = R.id.organization_text_input_layout;
      ThemeColorTextInputLayout organizationTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (organizationTextInputLayout == null) {
        break missingId;
      }

      id = R.id.organizational_unit;
      TextInputEditText organizationalUnit = ViewBindings.findChildViewById(rootView, id);
      if (organizationalUnit == null) {
        break missingId;
      }

      id = R.id.organizational_unit_text_input_layout;
      ThemeColorTextInputLayout organizationalUnitTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (organizationalUnitTextInputLayout == null) {
        break missingId;
      }

      id = R.id.password;
      TextInputEditText password = ViewBindings.findChildViewById(rootView, id);
      if (password == null) {
        break missingId;
      }

      id = R.id.password_text_input_layout;
      ThemeColorTextInputLayout passwordTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (passwordTextInputLayout == null) {
        break missingId;
      }

      id = R.id.signature_algorithms;
      AppCompatSpinner signatureAlgorithms = ViewBindings.findChildViewById(rootView, id);
      if (signatureAlgorithms == null) {
        break missingId;
      }

      id = R.id.state_or_province;
      TextInputEditText stateOrProvince = ViewBindings.findChildViewById(rootView, id);
      if (stateOrProvince == null) {
        break missingId;
      }

      id = R.id.state_or_province_text_input_layout;
      ThemeColorTextInputLayout stateOrProvinceTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (stateOrProvinceTextInputLayout == null) {
        break missingId;
      }

      id = R.id.street;
      TextInputEditText street = ViewBindings.findChildViewById(rootView, id);
      if (street == null) {
        break missingId;
      }

      id = R.id.street_text_input_layout;
      ThemeColorTextInputLayout streetTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (streetTextInputLayout == null) {
        break missingId;
      }

      id = R.id.type_bks;
      ThemeColorRadioButton typeBks = ViewBindings.findChildViewById(rootView, id);
      if (typeBks == null) {
        break missingId;
      }

      id = R.id.type_jks;
      ThemeColorRadioButton typeJks = ViewBindings.findChildViewById(rootView, id);
      if (typeJks == null) {
        break missingId;
      }

      id = R.id.type_radio_group;
      RadioGroup typeRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (typeRadioGroup == null) {
        break missingId;
      }

      id = R.id.validity_years;
      TextInputEditText validityYears = ViewBindings.findChildViewById(rootView, id);
      if (validityYears == null) {
        break missingId;
      }

      id = R.id.validity_years_text_input_layout;
      ThemeColorTextInputLayout validityYearsTextInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (validityYearsTextInputLayout == null) {
        break missingId;
      }

      return new DialogNewKeyStoreBinding((CardView) rootView, alias, aliasPassword,
          aliasPasswordTextInputLayout, aliasTextInputLayout, cancel, cityOrLocality,
          cityOrLocalityTextInputLayout, confirm, countryCode, countryCodeTextInputLayout, filename,
          filenameTextInputLayout, firstAndLastName, firstAndLastNameTextInputLayout, moreOptions,
          moreOptionsContainer, organization, organizationTextInputLayout, organizationalUnit,
          organizationalUnitTextInputLayout, password, passwordTextInputLayout, signatureAlgorithms,
          stateOrProvince, stateOrProvinceTextInputLayout, street, streetTextInputLayout, typeBks,
          typeJks, typeRadioGroup, validityYears, validityYearsTextInputLayout);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
