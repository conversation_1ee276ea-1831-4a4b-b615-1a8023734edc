package com.bm.atool.autojs.engine;

import android.content.Context;
import android.util.Log;

import com.bm.atool.autojs.api.APIBridge;
import com.bm.atool.autojs.model.ExecutionContext;
import com.bm.atool.autojs.model.Script;
import com.bm.atool.autojs.security.SecurityManager;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 脚本执行引擎
 * 负责管理脚本的执行、停止和监控
 */
public class ExecutionEngine {
    private static final String TAG = "ExecutionEngine";
    
    private Context context;
    private APIBridge apiBridge;
    private SecurityManager securityManager;
    // private AutoJs6Engine autoJs6Engine;  // 暂时禁用AutoJs6Engine

    // 执行管理
    private ExecutorService executorService;
    private ConcurrentHashMap<String, ExecutionContext> executionContexts;
    private ConcurrentHashMap<String, Future<?>> executionFutures;

    public ExecutionEngine(Context context, APIBridge apiBridge, SecurityManager securityManager) {
        this.context = context;
        this.apiBridge = apiBridge;
        this.securityManager = securityManager;

        // 初始化AutoJs6引擎 - 暂时禁用
        // this.autoJs6Engine = new AutoJs6Engine(context, apiBridge);
        
        // 初始化执行器
        this.executorService = Executors.newCachedThreadPool(new ScriptThreadFactory());
        this.executionContexts = new ConcurrentHashMap<>();
        this.executionFutures = new ConcurrentHashMap<>();
        
        Log.d(TAG, "ExecutionEngine initialized");
    }
    
    /**
     * 执行脚本
     */
    public ExecutionContext executeScript(Script script, ExecutionCallback callback) {
        if (script == null) {
            Log.e(TAG, "Cannot execute null script");
            return null;
        }
        
        try {
            // 创建执行上下文
            ExecutionContext context = new ExecutionContext(script.getId(), script);
            executionContexts.put(context.getExecutionId(), context);
            
            // 设置状态为启动中
            context.setState(ExecutionContext.ExecutionState.STARTING);
            
            Log.d(TAG, "Starting script execution: " + script.getName());
            
            // 提交执行任务
            Future<?> future = executorService.submit(() -> {
                executeScriptInternal(context, callback);
            });
            
            executionFutures.put(context.getExecutionId(), future);
            
            return context;
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting script execution: " + script.getName(), e);
            if (callback != null) {
                callback.onError(null, e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * 内部执行脚本
     */
    private void executeScriptInternal(ExecutionContext context, ExecutionCallback callback) {
        String executionId = context.getExecutionId();
        Script script = context.getScript();
        
        try {
            Log.d(TAG, "Executing script: " + script.getName());
            
            // 设置执行线程
            context.setExecutionThread(Thread.currentThread());
            context.setState(ExecutionContext.ExecutionState.RUNNING);
            
            // 通知开始执行
            if (callback != null) {
                callback.onStart(context);
            }
            
            // 检查是否被中断
            if (Thread.currentThread().isInterrupted()) {
                context.setState(ExecutionContext.ExecutionState.STOPPED);
                Log.d(TAG, "Script execution interrupted: " + script.getName());
                return;
            }
            
            // 使用AutoJs6引擎执行脚本 - 暂时使用模拟实现
            String result = executeScriptSimulated(script, context);
            
            // 检查执行结果
            if (Thread.currentThread().isInterrupted()) {
                context.setState(ExecutionContext.ExecutionState.STOPPED);
                Log.d(TAG, "Script execution stopped: " + script.getName());
                return;
            }
            
            // 设置结果和状态
            context.setResult(result);
            context.setState(ExecutionContext.ExecutionState.COMPLETED);
            
            Log.d(TAG, "Script execution completed: " + script.getName());
            
            // 通知执行成功
            if (callback != null) {
                callback.onSuccess(context, result);
            }
            
        } catch (InterruptedException e) {
            Log.d(TAG, "Script execution interrupted: " + script.getName());
            context.setState(ExecutionContext.ExecutionState.STOPPED);
            
        } catch (Exception e) {
            Log.e(TAG, "Script execution error: " + script.getName(), e);
            context.setException(e);
            context.setState(ExecutionContext.ExecutionState.ERROR);
            
            // 通知执行错误
            if (callback != null) {
                callback.onError(context, e.getMessage());
            }
            
        } finally {
            // 清理资源
            try {
                context.cleanup();
                executionContexts.remove(executionId);
                executionFutures.remove(executionId);
                
                // 更新内存使用情况
                updateMemoryUsage(context);
                
            } catch (Exception e) {
                Log.e(TAG, "Error during cleanup: " + script.getName(), e);
            }
        }
    }
    
    /**
     * 停止脚本执行
     */
    public boolean stopExecution(String executionId) {
        if (executionId == null || executionId.trim().isEmpty()) {
            Log.e(TAG, "Cannot stop execution with null or empty ID");
            return false;
        }
        
        try {
            ExecutionContext context = executionContexts.get(executionId);
            Future<?> future = executionFutures.get(executionId);
            
            if (context == null || future == null) {
                Log.w(TAG, "Execution not found: " + executionId);
                return false;
            }
            
            Log.d(TAG, "Stopping script execution: " + executionId);
            
            // 设置状态为停止中
            context.setState(ExecutionContext.ExecutionState.STOPPING);
            
            // 中断执行线程
            Thread executionThread = context.getExecutionThread();
            if (executionThread != null && executionThread.isAlive()) {
                executionThread.interrupt();
            }
            
            // 取消Future
            boolean cancelled = future.cancel(true);
            
            // 清理资源
            executionContexts.remove(executionId);
            executionFutures.remove(executionId);
            
            // 设置最终状态
            context.setState(ExecutionContext.ExecutionState.STOPPED);
            
            Log.d(TAG, "Script execution stopped: " + executionId + ", cancelled: " + cancelled);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping execution: " + executionId, e);
            return false;
        }
    }
    
    /**
     * 获取执行状态
     */
    public ExecutionContext.ExecutionState getExecutionStatus(String executionId) {
        ExecutionContext context = executionContexts.get(executionId);
        return context != null ? context.getState() : null;
    }
    
    /**
     * 获取执行上下文
     */
    public ExecutionContext getExecutionContext(String executionId) {
        return executionContexts.get(executionId);
    }
    
    /**
     * 获取运行中的执行数量
     */
    public int getRunningExecutionCount() {
        return executionContexts.size();
    }
    
    /**
     * 停止所有执行
     */
    public int stopAllExecutions() {
        int count = 0;
        for (String executionId : executionContexts.keySet()) {
            if (stopExecution(executionId)) {
                count++;
            }
        }
        Log.d(TAG, "Stopped " + count + " executions");
        return count;
    }
    
    /**
     * 更新内存使用情况
     */
    private void updateMemoryUsage(ExecutionContext context) {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            context.setMemoryUsage(usedMemory);
            
        } catch (Exception e) {
            Log.e(TAG, "Error updating memory usage", e);
        }
    }
    
    /**
     * 关闭执行引擎
     */
    public void shutdown() {
        try {
            Log.d(TAG, "Shutting down ExecutionEngine...");
            
            // 停止所有执行
            stopAllExecutions();
            
            // 关闭执行器
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
            }
            
            // 关闭AutoJs6引擎 - 暂时禁用
            // if (autoJs6Engine != null) {
            //     autoJs6Engine.shutdown();
            // }
            
            // 清理资源
            executionContexts.clear();
            executionFutures.clear();
            
            Log.d(TAG, "ExecutionEngine shutdown completed");
            
        } catch (Exception e) {
            Log.e(TAG, "Error during ExecutionEngine shutdown", e);
        }
    }
    
    /**
     * 脚本线程工厂
     */
    private static class ScriptThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix = "AutoJs-Script-";
        
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }
    
    /**
     * 模拟脚本执行 - 临时实现，等待AutoJs6集成完成
     */
    private String executeScriptSimulated(Script script, ExecutionContext context) throws InterruptedException {
        Log.d(TAG, "Simulating script execution: " + script.getName());

        // 模拟执行时间
        Thread.sleep(1000);

        // 模拟执行结果
        String result = "脚本 '" + script.getName() + "' 执行完成 (模拟)";
        Log.d(TAG, "Script simulation completed: " + result);

        return result;
    }

    /**
     * 执行回调接口
     */
    public interface ExecutionCallback {
        void onStart(ExecutionContext context);
        void onSuccess(ExecutionContext context, String result);
        void onError(ExecutionContext context, String error);
    }
}
