// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.TimePicker;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.github.aakira.expandablelayout.ExpandableRelativeLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorImageViewCompat;
import org.autojs.autojs.theme.widget.ThemeColorRadioButton;
import org.autojs.autojs.theme.widget.ThemeColorToolbar;
import org.autojs.autojs6.R;

public final class ActivityTimedTaskSettingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatEditText action;

  @NonNull
  public final RadioGroup broadcastGroup;

  @NonNull
  public final ThemeColorRadioButton dailyTaskRadio;

  @NonNull
  public final ExpandableRelativeLayout dailyTaskRelativeLayout;

  @NonNull
  public final TimePicker dailyTaskTimePicker;

  @NonNull
  public final TextView disposableTaskDate;

  @NonNull
  public final LinearLayout disposableTaskDateContainer;

  @NonNull
  public final ThemeColorImageViewCompat disposableTaskDateIcon;

  @NonNull
  public final ThemeColorRadioButton disposableTaskRadio;

  @NonNull
  public final TextView disposableTaskTime;

  @NonNull
  public final LinearLayout disposableTaskTimeContainer;

  @NonNull
  public final ThemeColorRadioButton runOnBatteryChange;

  @NonNull
  public final ThemeColorRadioButton runOnBoot;

  @NonNull
  public final ThemeColorRadioButton runOnBroadcast;

  @NonNull
  public final ThemeColorRadioButton runOnConfigChange;

  @NonNull
  public final ThemeColorRadioButton runOnConnChange;

  @NonNull
  public final ThemeColorRadioButton runOnCustomBroadcast;

  @NonNull
  public final ThemeColorRadioButton runOnHeadsetPlug;

  @NonNull
  public final ThemeColorRadioButton runOnPackageInstall;

  @NonNull
  public final ThemeColorRadioButton runOnPackageUninstall;

  @NonNull
  public final ThemeColorRadioButton runOnPackageUpdate;

  @NonNull
  public final ThemeColorRadioButton runOnPowerConnect;

  @NonNull
  public final ThemeColorRadioButton runOnPowerDisconnect;

  @NonNull
  public final ThemeColorRadioButton runOnScreenOff;

  @NonNull
  public final ThemeColorRadioButton runOnScreenOn;

  @NonNull
  public final ThemeColorRadioButton runOnScreenUnlock;

  @NonNull
  public final ThemeColorRadioButton runOnStartup;

  @NonNull
  public final ThemeColorRadioButton runOnTimeTick;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final RadioGroup timingGroup;

  @NonNull
  public final ThemeColorToolbar toolbar;

  @NonNull
  public final LinearLayout weeklyTaskContainer;

  @NonNull
  public final ThemeColorRadioButton weeklyTaskRadio;

  @NonNull
  public final TimePicker weeklyTaskTimePicker;

  private ActivityTimedTaskSettingBinding(@NonNull LinearLayout rootView,
      @NonNull AppCompatEditText action, @NonNull RadioGroup broadcastGroup,
      @NonNull ThemeColorRadioButton dailyTaskRadio,
      @NonNull ExpandableRelativeLayout dailyTaskRelativeLayout,
      @NonNull TimePicker dailyTaskTimePicker, @NonNull TextView disposableTaskDate,
      @NonNull LinearLayout disposableTaskDateContainer,
      @NonNull ThemeColorImageViewCompat disposableTaskDateIcon,
      @NonNull ThemeColorRadioButton disposableTaskRadio, @NonNull TextView disposableTaskTime,
      @NonNull LinearLayout disposableTaskTimeContainer,
      @NonNull ThemeColorRadioButton runOnBatteryChange, @NonNull ThemeColorRadioButton runOnBoot,
      @NonNull ThemeColorRadioButton runOnBroadcast,
      @NonNull ThemeColorRadioButton runOnConfigChange,
      @NonNull ThemeColorRadioButton runOnConnChange,
      @NonNull ThemeColorRadioButton runOnCustomBroadcast,
      @NonNull ThemeColorRadioButton runOnHeadsetPlug,
      @NonNull ThemeColorRadioButton runOnPackageInstall,
      @NonNull ThemeColorRadioButton runOnPackageUninstall,
      @NonNull ThemeColorRadioButton runOnPackageUpdate,
      @NonNull ThemeColorRadioButton runOnPowerConnect,
      @NonNull ThemeColorRadioButton runOnPowerDisconnect,
      @NonNull ThemeColorRadioButton runOnScreenOff, @NonNull ThemeColorRadioButton runOnScreenOn,
      @NonNull ThemeColorRadioButton runOnScreenUnlock, @NonNull ThemeColorRadioButton runOnStartup,
      @NonNull ThemeColorRadioButton runOnTimeTick, @NonNull ScrollView scrollView,
      @NonNull RadioGroup timingGroup, @NonNull ThemeColorToolbar toolbar,
      @NonNull LinearLayout weeklyTaskContainer, @NonNull ThemeColorRadioButton weeklyTaskRadio,
      @NonNull TimePicker weeklyTaskTimePicker) {
    this.rootView = rootView;
    this.action = action;
    this.broadcastGroup = broadcastGroup;
    this.dailyTaskRadio = dailyTaskRadio;
    this.dailyTaskRelativeLayout = dailyTaskRelativeLayout;
    this.dailyTaskTimePicker = dailyTaskTimePicker;
    this.disposableTaskDate = disposableTaskDate;
    this.disposableTaskDateContainer = disposableTaskDateContainer;
    this.disposableTaskDateIcon = disposableTaskDateIcon;
    this.disposableTaskRadio = disposableTaskRadio;
    this.disposableTaskTime = disposableTaskTime;
    this.disposableTaskTimeContainer = disposableTaskTimeContainer;
    this.runOnBatteryChange = runOnBatteryChange;
    this.runOnBoot = runOnBoot;
    this.runOnBroadcast = runOnBroadcast;
    this.runOnConfigChange = runOnConfigChange;
    this.runOnConnChange = runOnConnChange;
    this.runOnCustomBroadcast = runOnCustomBroadcast;
    this.runOnHeadsetPlug = runOnHeadsetPlug;
    this.runOnPackageInstall = runOnPackageInstall;
    this.runOnPackageUninstall = runOnPackageUninstall;
    this.runOnPackageUpdate = runOnPackageUpdate;
    this.runOnPowerConnect = runOnPowerConnect;
    this.runOnPowerDisconnect = runOnPowerDisconnect;
    this.runOnScreenOff = runOnScreenOff;
    this.runOnScreenOn = runOnScreenOn;
    this.runOnScreenUnlock = runOnScreenUnlock;
    this.runOnStartup = runOnStartup;
    this.runOnTimeTick = runOnTimeTick;
    this.scrollView = scrollView;
    this.timingGroup = timingGroup;
    this.toolbar = toolbar;
    this.weeklyTaskContainer = weeklyTaskContainer;
    this.weeklyTaskRadio = weeklyTaskRadio;
    this.weeklyTaskTimePicker = weeklyTaskTimePicker;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTimedTaskSettingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTimedTaskSettingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_timed_task_setting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTimedTaskSettingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.action;
      AppCompatEditText action = ViewBindings.findChildViewById(rootView, id);
      if (action == null) {
        break missingId;
      }

      id = R.id.broadcast_group;
      RadioGroup broadcastGroup = ViewBindings.findChildViewById(rootView, id);
      if (broadcastGroup == null) {
        break missingId;
      }

      id = R.id.daily_task_radio;
      ThemeColorRadioButton dailyTaskRadio = ViewBindings.findChildViewById(rootView, id);
      if (dailyTaskRadio == null) {
        break missingId;
      }

      id = R.id.daily_task_relative_layout;
      ExpandableRelativeLayout dailyTaskRelativeLayout = ViewBindings.findChildViewById(rootView, id);
      if (dailyTaskRelativeLayout == null) {
        break missingId;
      }

      id = R.id.daily_task_time_picker;
      TimePicker dailyTaskTimePicker = ViewBindings.findChildViewById(rootView, id);
      if (dailyTaskTimePicker == null) {
        break missingId;
      }

      id = R.id.disposable_task_date;
      TextView disposableTaskDate = ViewBindings.findChildViewById(rootView, id);
      if (disposableTaskDate == null) {
        break missingId;
      }

      id = R.id.disposable_task_date_container;
      LinearLayout disposableTaskDateContainer = ViewBindings.findChildViewById(rootView, id);
      if (disposableTaskDateContainer == null) {
        break missingId;
      }

      id = R.id.disposable_task_date_icon;
      ThemeColorImageViewCompat disposableTaskDateIcon = ViewBindings.findChildViewById(rootView, id);
      if (disposableTaskDateIcon == null) {
        break missingId;
      }

      id = R.id.disposable_task_radio;
      ThemeColorRadioButton disposableTaskRadio = ViewBindings.findChildViewById(rootView, id);
      if (disposableTaskRadio == null) {
        break missingId;
      }

      id = R.id.disposable_task_time;
      TextView disposableTaskTime = ViewBindings.findChildViewById(rootView, id);
      if (disposableTaskTime == null) {
        break missingId;
      }

      id = R.id.disposable_task_time_container;
      LinearLayout disposableTaskTimeContainer = ViewBindings.findChildViewById(rootView, id);
      if (disposableTaskTimeContainer == null) {
        break missingId;
      }

      id = R.id.run_on_battery_change;
      ThemeColorRadioButton runOnBatteryChange = ViewBindings.findChildViewById(rootView, id);
      if (runOnBatteryChange == null) {
        break missingId;
      }

      id = R.id.run_on_boot;
      ThemeColorRadioButton runOnBoot = ViewBindings.findChildViewById(rootView, id);
      if (runOnBoot == null) {
        break missingId;
      }

      id = R.id.run_on_broadcast;
      ThemeColorRadioButton runOnBroadcast = ViewBindings.findChildViewById(rootView, id);
      if (runOnBroadcast == null) {
        break missingId;
      }

      id = R.id.run_on_config_change;
      ThemeColorRadioButton runOnConfigChange = ViewBindings.findChildViewById(rootView, id);
      if (runOnConfigChange == null) {
        break missingId;
      }

      id = R.id.run_on_conn_change;
      ThemeColorRadioButton runOnConnChange = ViewBindings.findChildViewById(rootView, id);
      if (runOnConnChange == null) {
        break missingId;
      }

      id = R.id.run_on_custom_broadcast;
      ThemeColorRadioButton runOnCustomBroadcast = ViewBindings.findChildViewById(rootView, id);
      if (runOnCustomBroadcast == null) {
        break missingId;
      }

      id = R.id.run_on_headset_plug;
      ThemeColorRadioButton runOnHeadsetPlug = ViewBindings.findChildViewById(rootView, id);
      if (runOnHeadsetPlug == null) {
        break missingId;
      }

      id = R.id.run_on_package_install;
      ThemeColorRadioButton runOnPackageInstall = ViewBindings.findChildViewById(rootView, id);
      if (runOnPackageInstall == null) {
        break missingId;
      }

      id = R.id.run_on_package_uninstall;
      ThemeColorRadioButton runOnPackageUninstall = ViewBindings.findChildViewById(rootView, id);
      if (runOnPackageUninstall == null) {
        break missingId;
      }

      id = R.id.run_on_package_update;
      ThemeColorRadioButton runOnPackageUpdate = ViewBindings.findChildViewById(rootView, id);
      if (runOnPackageUpdate == null) {
        break missingId;
      }

      id = R.id.run_on_power_connect;
      ThemeColorRadioButton runOnPowerConnect = ViewBindings.findChildViewById(rootView, id);
      if (runOnPowerConnect == null) {
        break missingId;
      }

      id = R.id.run_on_power_disconnect;
      ThemeColorRadioButton runOnPowerDisconnect = ViewBindings.findChildViewById(rootView, id);
      if (runOnPowerDisconnect == null) {
        break missingId;
      }

      id = R.id.run_on_screen_off;
      ThemeColorRadioButton runOnScreenOff = ViewBindings.findChildViewById(rootView, id);
      if (runOnScreenOff == null) {
        break missingId;
      }

      id = R.id.run_on_screen_on;
      ThemeColorRadioButton runOnScreenOn = ViewBindings.findChildViewById(rootView, id);
      if (runOnScreenOn == null) {
        break missingId;
      }

      id = R.id.run_on_screen_unlock;
      ThemeColorRadioButton runOnScreenUnlock = ViewBindings.findChildViewById(rootView, id);
      if (runOnScreenUnlock == null) {
        break missingId;
      }

      id = R.id.run_on_startup;
      ThemeColorRadioButton runOnStartup = ViewBindings.findChildViewById(rootView, id);
      if (runOnStartup == null) {
        break missingId;
      }

      id = R.id.run_on_time_tick;
      ThemeColorRadioButton runOnTimeTick = ViewBindings.findChildViewById(rootView, id);
      if (runOnTimeTick == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.timing_group;
      RadioGroup timingGroup = ViewBindings.findChildViewById(rootView, id);
      if (timingGroup == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.weekly_task_container;
      LinearLayout weeklyTaskContainer = ViewBindings.findChildViewById(rootView, id);
      if (weeklyTaskContainer == null) {
        break missingId;
      }

      id = R.id.weekly_task_radio;
      ThemeColorRadioButton weeklyTaskRadio = ViewBindings.findChildViewById(rootView, id);
      if (weeklyTaskRadio == null) {
        break missingId;
      }

      id = R.id.weekly_task_time_picker;
      TimePicker weeklyTaskTimePicker = ViewBindings.findChildViewById(rootView, id);
      if (weeklyTaskTimePicker == null) {
        break missingId;
      }

      return new ActivityTimedTaskSettingBinding((LinearLayout) rootView, action, broadcastGroup,
          dailyTaskRadio, dailyTaskRelativeLayout, dailyTaskTimePicker, disposableTaskDate,
          disposableTaskDateContainer, disposableTaskDateIcon, disposableTaskRadio,
          disposableTaskTime, disposableTaskTimeContainer, runOnBatteryChange, runOnBoot,
          runOnBroadcast, runOnConfigChange, runOnConnChange, runOnCustomBroadcast,
          runOnHeadsetPlug, runOnPackageInstall, runOnPackageUninstall, runOnPackageUpdate,
          runOnPowerConnect, runOnPowerDisconnect, runOnScreenOff, runOnScreenOn, runOnScreenUnlock,
          runOnStartup, runOnTimeTick, scrollView, timingGroup, toolbar, weeklyTaskContainer,
          weeklyTaskRadio, weeklyTaskTimePicker);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
