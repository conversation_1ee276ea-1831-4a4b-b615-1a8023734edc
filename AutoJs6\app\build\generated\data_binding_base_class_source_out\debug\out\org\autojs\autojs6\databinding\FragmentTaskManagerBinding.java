// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorSwipeRefreshLayout;
import org.autojs.autojs.ui.main.task.TaskListRecyclerView;
import org.autojs.autojs6.R;

public final class FragmentTaskManagerBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ThemeColorSwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final TaskListRecyclerView taskList;

  private FragmentTaskManagerBinding(@NonNull FrameLayout rootView,
      @NonNull ThemeColorSwipeRefreshLayout swipeRefreshLayout,
      @NonNull TaskListRecyclerView taskList) {
    this.rootView = rootView;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.taskList = taskList;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTaskManagerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTaskManagerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_task_manager, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTaskManagerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.swipe_refresh_layout;
      ThemeColorSwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.task_list;
      TaskListRecyclerView taskList = ViewBindings.findChildViewById(rootView, id);
      if (taskList == null) {
        break missingId;
      }

      return new FragmentTaskManagerBinding((FrameLayout) rootView, swipeRefreshLayout, taskList);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
