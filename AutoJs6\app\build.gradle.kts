plugins {
    id("com.android.library")
    id("com.google.devtools.ksp")
    id("org.jetbrains.kotlin.android")
}

val globalApplicationId = "org.autojs.autojs6"

dependencies /* Unclassified */ {
    // Compose
    // implementation("androidx.compose.ui:ui-android:1.6.7")

    // Kotlin reflect
    implementation(kotlin("reflect"))

    // LeakCanary
    debugImplementation("com.squareup.leakcanary:leakcanary-android:2.14")

    // Android supports
    implementation("androidx.cardview:cardview:1.0.0")
    implementation("androidx.multidex:multidex:2.0.1")

    // Material Components
    implementation("com.google.android.material:material:1.12.0")

    // SwipeRefreshLayout
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")

    // ConstraintLayout
    implementation("androidx.constraintlayout:constraintlayout:2.2.1")

    // FlexboxLayout
    implementation("com.google.android.flexbox:flexbox:3.0.0")

    // Common Markdown
    implementation("com.github.atlassian:commonmark-java:commonmark-parent-0.9.0")

    // Flexmark Java HTML to Markdown Extensible Converter
    implementation("com.vladsch.flexmark:flexmark-html2md-converter:0.64.8")

    // Licenses Dialog
    implementation("de.psdev.licensesdialog:licensesdialog:2.2.0")

    // Apache Commons
    implementation("org.apache.commons:commons-lang3:3.16.0")

    // Retrofit
    implementation("com.squareup.retrofit2:retrofit:2.11.0")
    implementation("com.squareup.retrofit2:converter-gson:2.11.0")
    implementation("com.squareup.retrofit2:adapter-rxjava2:2.11.0")
    implementation("com.jakewharton.retrofit:retrofit2-kotlin-coroutines-adapter:0.9.2")

    // Glide
    implementation("com.github.bumptech.glide:glide:4.16.0")
    ksp("com.github.bumptech.glide:ksp:4.16.0")

    // Joda Time
    implementation("joda-time:joda-time:2.12.7")

    // Flurry
    implementation("com.flurry.android:analytics:14.4.0")

    // Bugly - using external dependency instead of local project
    // implementation(project(":libs:com.tencent.bugly.crashreport-4.0.4"))

    // OkHttp
    // implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.12")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")

    // Webkit
    implementation("androidx.webkit:webkit:1.13.0")

    // Gson
    implementation("com.google.code.gson:gson:2.11.0")

    // Zip4j
    implementation("net.lingala.zip4j:zip4j:2.11.5")

    // Log4j
    // FIXME by SuperMonster003 on Aug 14, 2024.
    //  ! Vulnerable dependency (5 vulnerabilities) for log4j (version 1):
    //  ! - CVE-2022-23307, Score: 8.8
    //  ! - CVE-2022-23305, Score: 9.8
    //  ! - CVE-2022-23302, Score: 8.8
    //  ! - CVE-2021-4104, Score: 7.5
    //  ! - CVE-2019-17571, Score: 9.8
    //  ! However, log4j version 2 which requires Android API Level not lower than 26
    //  ! is not compatible with current project with min API Level 24.
    //  !
    //  ! zh-CN:
    //  !
    //  ! 依赖库 log4j (第一版本) 是易受攻击的 (含 5 项漏洞):
    //  ! - CVE-2022-23307, 评分: 8.8
    //  ! - CVE-2022-23305, 评分: 9.8
    //  ! - CVE-2022-23302, 评分: 8.8
    //  ! - CVE-2021-4104, 评分: 7.5
    //  ! - CVE-2019-17571, 评分: 9.8
    //  ! 但 log4j 第二版本要求安卓 API 级别不低于 26,
    //  ! 与最低 API 级别为 24 的当前项目无法兼容.
    implementation("log4j:log4j:1.2.17")

    // Android Logging Log4j
    implementation("de.mindpipe.android:android-logging-log4j:1.0.3")

    // Preference
    implementation("androidx.preference:preference-ktx:1.2.1")

    // RootShell
    // implementation("com.github.Stericson:RootShell:1.6")
    // implementation(project(":libs:root-shell-1.6"))

    // JDeferred
    implementation("org.jdeferred:jdeferred-android-aar:1.2.6")

    // Rx
    implementation("io.reactivex.rxjava2:rxjava:2.2.21")
    implementation("io.reactivex.rxjava2:rxandroid:2.1.1@aar")

    // Device Names
    implementation("com.jaredrummler:android-device-names:2.1.1")

    // Version Compare
    implementation("io.github.g00fy2:versioncompare:1.5.0")

    // Terminal Emulator
    // implementation(project(":libs:jackpal.androidterm.libtermexec-1.0"))
    // implementation(project(":libs:jackpal.androidterm.emulatorview-1.0.42"))
    // implementation(project(":libs:jackpal.androidterm-1.0.70"))

    // Dex
    // implementation(files("$rootDir/libs/com.android.dx-1.14.jar"))
    // implementation(files("$rootDir/libs/com.legacy.android.dx-1.7.0.jar"))

    // OpenCV
    // implementation(project(":libs:org.opencv-4.8.0"))

    // PaddleOCR
    // implementation(project(":libs:paddleocr"))

    // RapidOCR
    // implementation(project(":libs:rapidocr"))

    // Image Quantization
    // implementation(project(":libs:imagequant"))

    // Android Job
    // implementation(project(":libs:android-job-simplified-1.4.3"))

    // APK Parser
    // implementation("com.jaredrummler:apk-parser:1.0.2")
    // implementation("com.github.AndroidDeveloperLB:apk-parser:6")
    // implementation(project(":modules:apk-parser"))

    // Prism4j
    // implementation(files("$rootDir/libs/prism4j-2.0.0.jar"))
    // implementation(files("$rootDir/libs/prism4j-bundler-2.0.0.jar"))
    // implementation(project(":libs:markwon-core-4.6.2"))
    // implementation(project(":libs:markwon-syntax-highlight-4.6.2"))

    // Rhino
    // implementation(files("$rootDir/libs/org.mozilla.rhino-1.8.1-SNAPSHOT.jar"))

    // Tasker Plugin
    // implementation(project(":libs:android-spackle-9.0.0"))
    // implementation(project(":libs:android-assertion-9.0.0"))
    // implementation(project(":libs:android-plugin-client-sdk-for-locale-9.0.0"))

    // JavaMail for Android
    // implementation(files("$rootDir/libs/javamail-android/activation.jar"))
    // implementation(files("$rootDir/libs/javamail-android/additionnal.jar"))
    // implementation(files("$rootDir/libs/javamail-android/mail.jar"))

    // Shizuku
    implementation("dev.rikka.shizuku:api:13.1.5")
    implementation("dev.rikka.shizuku:provider:13.1.5")

    // ARSCLib
    implementation("io.github.reandroid:ARSCLib:1.3.1")

    // Toaster
    implementation("com.github.getActivity:Toaster:12.6")
    implementation("com.github.getActivity:EasyWindow:10.3")

    // Pinyin4j
    implementation("com.belerweb:pinyin4j:2.5.1")

    // Jieba Analysis (zh-CN: 结巴分词)
    // implementation("com.huaban:jieba-analysis:1.0.2")
    // implementation(project(":modules:jieba-analysis"))

    // Tiny Sign
    // implementation(files("$rootDir/libs/tiny-sign-0.9.jar"))

    // Room
    implementation("androidx.room:room-runtime:2.7.1")
    implementation("androidx.room:room-ktx:2.7.1")
    ksp("androidx.room:room-compiler:2.7.1")

    // ApkSig
    // implementation("com.android.tools.build:apksig:8.7.3")

    // ApkSigner
    // implementation(project(":modules:apk-signer"))

    // Spongy Castle
    implementation("com.madgag.spongycastle:prov:********")

    // MQTT
    implementation("org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.1.0")
    implementation("org.eclipse.paho:org.eclipse.paho.android.service:1.1.1")

    // Jsoup
    implementation("org.jsoup:jsoup:1.19.1")

    // Material Date Time Picker
    // implementation(project(":modules:material-date-time-picker"))

    // ICU4J
    implementation("com.ibm.icu:icu4j:77.1")
}

dependencies /* MIME */ {
    // @Hint by SuperMonster003 on Oct 5, 2023.
    //  ! Only for Android API 26 (8.0) [O] and above.
    //  ! zh-CN: 仅适用于安卓 API 级别 26 (8.0) [O] 及以上.
    // Apache Tika Core
    // implementation("org.apache.tika:tika-core:2.9.2")

    // MIME Util
    // implementation("eu.medsea.mimeutil:mime-util:2.1.3")
    implementation(files("$projectDir/../libs/mime-util-2.1.3.jar"))
}

dependencies /* Test */ {
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test:runner:1.6.2")
    androidTestImplementation("org.junit.jupiter:junit-jupiter:5.10.3")
}

dependencies /* Annotations */ {
    // Android Annotations
    implementation("org.androidannotations:androidannotations-api:4.8.0")
    implementation("androidx.annotation:annotation:1.9.1")
    ksp("org.androidannotations:androidannotations:4.8.0")

    // JCIP Annotations
    implementation("net.jcip:jcip-annotations:1.0")

    // EventBus
    implementation("org.greenrobot:eventbus:3.3.1")
}

dependencies /* AppCompat */ {
    // @Hint by SuperMonster003 on Oct 5, 2023.
    //  ! To check the releases for Appcompat library,
    //  ! visit https://developer.android.com/jetpack/androidx/releases/appcompat.
    //  ! zh-CN:
    //  ! 查看 Appcompat 库的发行版本,
    //  ! 可访问 https://developer.android.com/jetpack/androidx/releases/appcompat.
    implementation("androidx.appcompat:appcompat:1.7.0")

    // AppCompat for legacy views (such as JsTextViewLegacy)
    // implementation(project(":libs:androidx.appcompat-1.0.2")) {
    //     setVersion("1.0.2")
    // }
}

dependencies /* Material Dialogs */ {
    // Material Dialogs
    // TODO by SuperMonster003 on Feb 5, 2022.
    //  ! Upgrade to 3.3.0 (more difficult than expected).
    //  ! zh-CN: 升级至 3.3.0 (实际难度超出预期较多).
    //  # val configuration: (ExternalModuleDependency).() -> Unit = {
    //  #     version {
    //  #         prefer("0.9.6.0")
    //  #         because("Not ready to update to version 3.3.0 yet")
    //  #     }
    //  # }
    //  # configuration.let { cfg ->
    //  #     implementation("com.afollestad.material-dialogs:core", cfg)
    //  #     implementation("com.afollestad.material-dialogs:commons", cfg)
    //  # }
    // implementation(project(":modules:material-dialogs"))
    implementation("me.zhanghai.android.materialprogressbar:library:1.4.2")
}

dependencies /* Layout */ {
    // Expandable Layout
    implementation("com.github.aakira:expandable-layout:1.6.0")

    // Expandable RecyclerView
    implementation("com.bignerdranch.android:expandablerecyclerview:3.0.0-RC1")

    // Flexible Divider
    implementation("com.yqritc:recyclerview-flexibledivider:1.4.0")
}

dependencies /* View */ {
    // RoundedImageView
    implementation("com.makeramen:roundedimageview:2.3.0")

    // CircleImageView
    implementation("de.hdodenhof:circleimageview:3.1.0")

    // Animated SVG
    implementation("com.jaredrummler:animated-svg-view:1.0.6")
}

dependencies /* GitHub API */ {
    implementation(files("$projectDir/../libs/github-api-1.306.jar"))

    implementation("commons-io:commons-io") {
        because("Compatibility for Android API Level < 26 (Android 8.0) [O]")
        version {
            strictly("2.8.0")
            because("Exception on newer versions: 'NoClassDefFoundError: org.apache.commons.io.IOUtils'")
        }
    }

    implementation("com.fasterxml.jackson.core:jackson-databind") {
        because("Compatibility for Android API Level < 26 (Android 8.0) [O]")
        version {
            strictly("2.13.4.2")
            because("Exception on 2.14.x: 'No virtual method getParameterCount()I in class Ljava/lang/reflect/Method'")
        }
    }

    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4") {
        because("Compatibility of java.time.* for Android API Level < 26 (Android 8.0) [O]")
    }
}

dependencies /* MLKit */ {
    // OCR
    implementation("com.google.mlkit:text-recognition-chinese:16.0.1")

    // Barcode
    implementation("com.google.mlkit:barcode-scanning:17.3.0")
}

dependencies /* OpenCC */ {
    // OpenCC
    implementation("com.github.qichuan:android-opencc:1.2.0")
}

dependencies /* Auto.js Extensions */ {
    // Settings Compat
    // @Integrated by SuperMonster003 on Mar 30, 2023.
    //  # implementation("com.github.hyb1996:settingscompat:1.1.5")

    // Enhanced Floaty
    // @Integrated by SuperMonster003 on Mar 30, 2023.
    //  # implementation("com.github.hyb1996:EnhancedFloaty:0.31")

    // MultiLevelListView
    // @Integrated by SuperMonster003 on Mar 30, 2023.
    //  # implementation("com.github.hyb1996:android-multi-level-listview:1.1")

    // Auto.js APK Builder
    // @Integrated by SuperMonster003 on Mar 30, 2023.
    //  # implementation(project(":libs:Auto.js-ApkBuilder-1.0.3"))

    // Extracted from com.github.hyb1996:MutableTheme:1.0.0
    implementation("androidx.recyclerview:recyclerview:1.4.0")
    implementation("com.github.ozodrukh:CircularReveal:2.0.1")
    // @Legacy com.jrummyapps:colorpicker:2.1.7
    // @Integrated by SuperMonster003 on Mar 25, 2025.
    //  # implementation("com.jaredrummler:colorpicker:1.1.0")
    // implementation(project(":modules:color-picker"))
}

dependencies /* Archived */ {
    // Kotlin
    // @Comment by SuperMonster003 on May 19, 2022.
    //  ! It is no longer necessary to declare a dependency on the stdlib library in any Kotlin Gradle project.
    //  ! The dependency is added by default.
    //  ! See https://kotlinlang.org/docs/gradle.html#dependency-on-the-standard-library.
    //  ! zh-CN:
    //  ! 已无需在 Kotlin Gradle 项目中显式声明标准库 (stdlib).
    //  ! 相关依赖已默认被添加.
    //  ! 参阅 https://kotlinlang.org/docs/gradle.html#dependency-on-the-standard-library.
    // implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.21")

    // Google Guava
    // @Comment by SuperMonster003 on Jun 1, 2022.
    //  ! Not necessary for current project but worth keeping the trace.
    //  ! zh-CN: 于当前项目已不再需要, 但依然值得留存其踪迹 (以备不时之需).
    // implementation("com.google.guava:guava:31.1-jre")

    // Javax WS RS API (Java API for RESTful Web Services)
    // @Comment by SuperMonster003 on Apr 9, 2024.
    //  ! It was ever imported and used for MediaType constants.
    //  ! zh-CN: 曾用于 MediaType 常量的导入及使用.
    // implementation("javax.ws.rs:javax.ws.rs-api:2.1.1")
}

dependencies /* Reserved for auto append by IDE */ {

}

android {
    namespace = globalApplicationId
    compileSdk = 34

    defaultConfig {
        minSdk = 24
        targetSdk = 34
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled = true
    }

    sourceSets {
        getByName("main") {
            assets.srcDirs("src/main/assets")
        }
    }

    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    @Suppress("DEPRECATION")
    packagingOptions {
        resources.pickFirsts.addAll(listOf(
            "META-INF/DEPENDENCIES",
            "META-INF/LICENSE",
            "META-INF/*.kotlin_module"
        ))
    }

    kotlinOptions {
        jvmTarget = "11"
        // freeCompilerArgs = listOf("-Xjvm-default=all-compatibility")
    }

    lint {
        abortOnError = false
    }

    // Signing configs not needed for library module

    buildTypes {
        val proguardFiles = arrayOf<Any>(
            getDefaultProguardFile("proguard-android.txt"),
            "proguard-rules.pro",
        )
        debug {
            isMinifyEnabled = false
            proguardFiles(*proguardFiles)
        }
        release {
            isMinifyEnabled = false
            proguardFiles(*proguardFiles)
        }
    }

    buildFeatures {
        aidl = true
        viewBinding = true
        // @Hint by SuperMonster003 on Aug 14, 2023.
        //  ! Substitution of "android.defaults.buildfeatures.buildconfig=true"
        //  ! zh-CN: "android.defaults.buildfeatures.buildconfig=true" 的替代方案
        buildConfig = true
        // @Archived by SuperMonster003 on Sep 23, 2024.
        //  ! Jetpack Compose
        //  # compose = true
        //  # composeOptions {
        //  #     kotlinCompilerExtensionVersion = "1.5.12"
        //  # }
    }

    // Application variants and splits configuration not needed for library module

}

tasks {
    withType(JavaCompile::class.java) {
        options.encoding = "UTF-8"

        // @Hint by SuperMonster003 on May 18, 2022.
        //  ! Comment or remove this option if you are tired of plenty of warnings. :)
        //  ! zh-CN: 注释或移除此选项可避免过多警告消息造成的困扰. [笑脸符号]
        // options.compilerArgs.addAll(listOf("-Xlint:deprecation", "-Xlint:unchecked"))
    }

    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions { jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_11) }
    }

    // Copy task removed for library module
}

// Library module configuration complete
