package com.bm.autojs6.module.model;

/**
 * 脚本执行结果
 */
public class ScriptResult {
    private String scriptId;
    private boolean success;
    private String result;
    private String error;
    private long executeTime;
    private long duration;
    
    private ScriptResult(String scriptId, boolean success) {
        this.scriptId = scriptId;
        this.success = success;
        this.executeTime = System.currentTimeMillis();
    }
    
    /**
     * 创建成功结果
     */
    public static ScriptResult success(String scriptId, String result) {
        ScriptResult scriptResult = new ScriptResult(scriptId, true);
        scriptResult.result = result;
        return scriptResult;
    }
    
    /**
     * 创建错误结果
     */
    public static ScriptResult error(String scriptId, String error) {
        ScriptResult scriptResult = new ScriptResult(scriptId, false);
        scriptResult.error = error;
        return scriptResult;
    }
    
    // Getters
    public String getScriptId() {
        return scriptId;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getResult() {
        return result;
    }
    
    public String getError() {
        return error;
    }
    
    public long getExecuteTime() {
        return executeTime;
    }
    
    public long getDuration() {
        return duration;
    }
    
    public void setDuration(long duration) {
        this.duration = duration;
    }
    
    @Override
    public String toString() {
        return "ScriptResult{" +
                "scriptId='" + scriptId + '\'' +
                ", success=" + success +
                ", result='" + result + '\'' +
                ", error='" + error + '\'' +
                ", executeTime=" + executeTime +
                ", duration=" + duration +
                '}';
    }
}
