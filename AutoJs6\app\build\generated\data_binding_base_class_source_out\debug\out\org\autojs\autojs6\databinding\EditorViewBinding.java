// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorToolbarEditor;
import org.autojs.autojs.ui.edit.completion.CodeCompletionBar;
import org.autojs.autojs.ui.edit.debug.DebugBar;
import org.autojs.autojs.ui.edit.editor.CodeEditor;
import org.autojs.autojs.ui.edit.keyboard.FunctionsKeyboardView;
import org.autojs.autojs.ui.widget.EWebView;
import org.autojs.autojs6.R;

public final class EditorViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CodeCompletionBar codeCompletionBar;

  @NonNull
  public final DebugBar debugBar;

  @NonNull
  public final EWebView docs;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final CodeEditor editor;

  @NonNull
  public final ImageView functions;

  @NonNull
  public final FunctionsKeyboardView functionsKeyboard;

  @NonNull
  public final RelativeLayout inputMethodEnhanceBar;

  @NonNull
  public final CodeCompletionBar symbolBar;

  @NonNull
  public final ThemeColorToolbarEditor toolbar;

  @NonNull
  public final FrameLayout toolbarMenu;

  private EditorViewBinding(@NonNull LinearLayout rootView,
      @NonNull CodeCompletionBar codeCompletionBar, @NonNull DebugBar debugBar,
      @NonNull EWebView docs, @NonNull DrawerLayout drawerLayout, @NonNull CodeEditor editor,
      @NonNull ImageView functions, @NonNull FunctionsKeyboardView functionsKeyboard,
      @NonNull RelativeLayout inputMethodEnhanceBar, @NonNull CodeCompletionBar symbolBar,
      @NonNull ThemeColorToolbarEditor toolbar, @NonNull FrameLayout toolbarMenu) {
    this.rootView = rootView;
    this.codeCompletionBar = codeCompletionBar;
    this.debugBar = debugBar;
    this.docs = docs;
    this.drawerLayout = drawerLayout;
    this.editor = editor;
    this.functions = functions;
    this.functionsKeyboard = functionsKeyboard;
    this.inputMethodEnhanceBar = inputMethodEnhanceBar;
    this.symbolBar = symbolBar;
    this.toolbar = toolbar;
    this.toolbarMenu = toolbarMenu;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static EditorViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static EditorViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.editor_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static EditorViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.code_completion_bar;
      CodeCompletionBar codeCompletionBar = ViewBindings.findChildViewById(rootView, id);
      if (codeCompletionBar == null) {
        break missingId;
      }

      id = R.id.debug_bar;
      DebugBar debugBar = ViewBindings.findChildViewById(rootView, id);
      if (debugBar == null) {
        break missingId;
      }

      id = R.id.docs;
      EWebView docs = ViewBindings.findChildViewById(rootView, id);
      if (docs == null) {
        break missingId;
      }

      id = R.id.drawer_layout;
      DrawerLayout drawerLayout = ViewBindings.findChildViewById(rootView, id);
      if (drawerLayout == null) {
        break missingId;
      }

      id = R.id.editor;
      CodeEditor editor = ViewBindings.findChildViewById(rootView, id);
      if (editor == null) {
        break missingId;
      }

      id = R.id.functions;
      ImageView functions = ViewBindings.findChildViewById(rootView, id);
      if (functions == null) {
        break missingId;
      }

      id = R.id.functions_keyboard;
      FunctionsKeyboardView functionsKeyboard = ViewBindings.findChildViewById(rootView, id);
      if (functionsKeyboard == null) {
        break missingId;
      }

      id = R.id.input_method_enhance_bar;
      RelativeLayout inputMethodEnhanceBar = ViewBindings.findChildViewById(rootView, id);
      if (inputMethodEnhanceBar == null) {
        break missingId;
      }

      id = R.id.symbol_bar;
      CodeCompletionBar symbolBar = ViewBindings.findChildViewById(rootView, id);
      if (symbolBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbarEditor toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.toolbar_menu;
      FrameLayout toolbarMenu = ViewBindings.findChildViewById(rootView, id);
      if (toolbarMenu == null) {
        break missingId;
      }

      return new EditorViewBinding((LinearLayout) rootView, codeCompletionBar, debugBar, docs,
          drawerLayout, editor, functions, functionsKeyboard, inputMethodEnhanceBar, symbolBar,
          toolbar, toolbarMenu);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
