-- Merging decision tree log ---
manifest
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-224:12
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-224:12
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-224:12
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-224:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\be6bbc30661cacc9681da4493f1624bf\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\b0a02ef2865f8b1ab6fcd0755a3cd915\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\f76b3476b820445629bb44fd97cdba1c\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\35eea97605e8f8ac8a957559700c83be\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\bca5d58794b22aca6036379432e392dd\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d8a17e80b34dfeac4d4324ceeb8814\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d96eae6c30f38e251d287505e529eac3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\466bd753c0bc663f243f57f85dfa0cbd\transformed\preference-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e9069ecca228dca1e9503696b9c910da\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b1b6d71ac725a2fc4ab91a425e45a1c\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\31e366bfae40842c9679e2edb5cfb3f7\transformed\safe-parcel-1.7.0\AndroidManifest.xml:6:1-14:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\50cc86ed3c6197819ca1f984760c144b\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\92658cbff1b755dc31e432292d3e02b3\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f897afc1102d841ba1635c00b08f1354\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\e88a25488b9f006132cae73aba61784a\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\996035a061487a70b28bf5eee4e4e32d\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\acf1eb6355a0aa51fff0c9886c913059\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ff88e707fd1ad5cea819bc2df513d9f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\0c7728c0dbc7c5197515014cd79b6b95\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\03ddd522988da343dbe79f2302dcd006\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\19059f77d33f7f3ee99aa46c6349a71c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\339ca94cdafe38a77bfd905e2704f20e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\85a5a08400df38977c0ecf718ef11a2c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\df523c34031b8b51fceccd8e6c5704d7\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\384cb12f5073881ae43b8877bf01735a\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\840970417fed3f773a2fe740b38bd53a\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fc62e522023f0ae3c150e3bb85b5180d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f220744e8e6e74085c3f48ebfa7d5d45\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8f9e326ffde6c021ca8a6b9f49456b0f\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\0fb22fd98bfcf3852f353526066948a4\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3905a59dd8d4010ee767d2164842210\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\483f913148bc9db67e36bb73b45ef125\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\65b9a7bc33494692978a1a522eeec93d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fb0fc75fdf2f5fe2613f79f3aca2fe6\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1d1b607e6712116afdd99f5448a1331e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6231b9f97c8ae5d2272d84e6569ead21\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a07013b9d0389473114f29be64bf39f5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\29e21a8ce910b658e4fb80a790e89f60\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\3fd97b68fd2cab07c2aede205d02f337\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e9781549dfe16481e4886d4f453fb678\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6dac5ab0b5c19f4ba9824e6dbb25d749\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6d6bc14ce48f3b94204266a4fad77eaa\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\62999619145cd22b7b45bc443dd83ef4\transformed\FreeReflection-3.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\0db9fee98cb4986175685be692ee59e3\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e2a52dc5b09baccf98389115e69331b3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f6cdcd292ac6ea0e1583eb98bbebf52\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0ec72d84172d3a6e9a28c1f087724aa7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\257f149158ae9891e31bf1007eee8575\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c5afe4edb7ca63a225899e49783f60cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c15531773577e3540f4a3e218e27c149\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\edbe1198ab35d6c14d19da5bfeac10e4\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\26b6109143b84f80832baac7b85c75a6\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\94cf0955da1394be7fa131abcac7297f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7407a8c36690a03b2cdbd44f46565f3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.telephony
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:5:5-7:36
	android:required
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-33
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:6:9-50
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:9:5-10:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:10:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:9:22-83
uses-permission#android.permission.RECEIVE_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:11:5-70
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:11:22-67
uses-permission#android.permission.READ_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-67
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-64
uses-permission#android.permission.SEND_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.WRITE_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-68
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-65
uses-permission#android.permission.INTERNET
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-67
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-64
uses-permission#android.permission.READ_PHONE_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-75
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-72
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-76
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-68
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-65
permission#android.permission.DEVICE_POWER
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-21:51
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:21:9-48
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:17-63
uses-permission#android.permission.BIND_JOB_SERVICE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:22:5-23:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:23:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:22:22-72
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:24:5-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:24:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-76
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:28:5-80
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-80
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-80
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:28:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-74
uses-permission#android.permission.WRITE_MEDIA_IMAGES
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-78
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:34:5-92
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:34:22-89
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-95
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-92
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-38:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:38:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-82
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:39:5-80
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:39:22-78
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:42:5-81
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:42:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:5-44:40
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:44:9-37
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:22-79
uses-permission#android.permission.VIBRATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:45:5-66
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:45:22-63
uses-permission#android.permission.CAMERA
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:5-65
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:5-71
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:22-68
uses-permission#android.permission.CAPTURE_VIDEO_OUTPUT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:5-49:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:49:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:22-76
uses-permission#android.permission.CAPTURE_SECURE_VIDEO_OUTPUT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:50:5-51:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:51:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:50:22-83
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:52:5-94
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:52:22-91
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:5-89
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:22-86
application
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:5-222:19
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:5-222:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d8a17e80b34dfeac4d4324ceeb8814\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d8a17e80b34dfeac4d4324ceeb8814\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d96eae6c30f38e251d287505e529eac3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d96eae6c30f38e251d287505e529eac3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\257f149158ae9891e31bf1007eee8575\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\257f149158ae9891e31bf1007eee8575\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c5afe4edb7ca63a225899e49783f60cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c5afe4edb7ca63a225899e49783f60cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:60:9-54
	android:icon
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:58:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:61:9-35
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:59:9-41
	android:fullBackupContent
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:57:9-54
	tools:targetApi
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:65:9-29
	android:allowBackup
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:55:9-35
	android:theme
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:62:9-49
	android:dataExtractionRules
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:56:9-65
	tools:replace
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:66:9-98
	android:usesCleartextTraffic
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:63:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:64:9-28
activity#com.bm.atool.LoginActivity
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:67:9-69:40
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:69:13-37
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:68:13-42
activity#com.bm.atool.MainActivity
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:70:9-83:20
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:73:13-45
	android:excludeFromRecents
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-46
	android:launchMode
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-44
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-36
	android:theme
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:74:13-53
	android:taskAffinity
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:71:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:78:13-82:29
action#android.intent.action.MAIN
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:79:17-69
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:79:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:81:17-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:81:27-74
activity#com.bm.atool.service.singlepixel.SinglePixelActivity
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:84:9-86:39
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:86:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:85:13-68
receiver#com.bm.atool.receivers.SimChangedReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:88:9-94:20
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:89:13-57
intent-filter#action:name:android.intent.action.SIM_STATE_CHANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-93:29
action#android.intent.action.SIM_STATE_CHANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:92:17-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:92:25-79
receiver#com.bm.atool.receivers.SmsReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:9-101:20
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:97:13-66
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:19-56
intent-filter#action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:13-100:29
action#android.provider.Telephony.SMS_RECEIVED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:17-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:25-79
receiver#com.bm.atool.receivers.WakeUpReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:103:9-116:20
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-37
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:104:13-53
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED+action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MEDIA_MOUNTED+action:name:android.intent.action.USER_PRESENT+action:name:android.intent.action.USER_PRESENT+action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:107:13-115:29
action#android.intent.action.USER_PRESENT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:17-76
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:25-74
action#android.intent.action.BOOT_COMPLETED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
action#android.intent.action.MEDIA_MOUNTED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:112:17-78
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:112:25-75
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:17-87
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-90
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-87
receiver#com.bm.atool.receivers.WakeUpAutoStartReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:117:9-144:20
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-37
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:120:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:118:13-62
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:122:13-125:29
intent-filter#action:name:android.intent.action.PACKAGE_ADDED+action:name:android.intent.action.PACKAGE_REMOVED+data:scheme:package
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:127:13-131:29
action#android.intent.action.PACKAGE_ADDED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:128:17-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:128:25-75
action#android.intent.action.PACKAGE_REMOVED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:17-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:25-77
data
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
	android:scheme
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE+action:name:android.net.wifi.STATE_CHANGE+action:name:android.net.wifi.WIFI_STATE_CJANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:133:13-137:29
action#android.net.wifi.WIFI_STATE_CJANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:135:17-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:135:25-75
action#android.net.wifi.STATE_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:17-71
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:25-69
intent-filter#action:name:android.intent.action.MEDIA_EJECT+action:name:android.intent.action.MEDIA_MOUNTED+data:scheme:file
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:139:13-143:29
action#android.intent.action.MEDIA_EJECT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140:17-75
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140:25-73
service#com.bm.atool.service.JobSchedulerService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:147:9-152:43
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:152:13-41
	android:enabled
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:150:13-35
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:151:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-69
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:148:13-56
service#com.bm.atool.service.WatchDogService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:154:9-159:43
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:159:13-41
	android:enabled
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:157:13-35
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:158:13-36
	android:foregroundServiceType
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:156:13-58
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:155:13-52
service#com.bm.atool.service.PlayMusicService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:161:9-163:46
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:163:13-44
	android:foregroundServiceType
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:162:13-58
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:161:18-58
service#com.bm.atool.service.ANTAccessibilityService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:164:9-177:19
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-45
	android:enabled
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:166:13-35
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:167:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:165:13-60
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-172:29
action#android.accessibilityservice.AccessibilityService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:17-92
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:25-89
meta-data#android.accessibilityservice
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:174:13-176:54
	android:resource
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:176:17-51
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:175:17-60
service#com.bm.atool.service.SocketService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:178:9-194:19
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:182:13-38
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:181:13-42
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:180:13-37
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-69
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:179:13-50
intent-filter#action:name:android.net.VpnService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:184:13-186:29
action#android.net.VpnService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:185:17-65
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:185:25-62
meta-data#android.net.VpnService.SUPPORTS_ALWAYS_ON
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:187:13-189:39
	android:value
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:189:17-37
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:188:17-73
property
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:190:13-192:38
	android:value
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:192:17-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:191:17-76
service#com.bm.atool.service.NotificationService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:196:9-204:19
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:198:13-45
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:200:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:199:13-87
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:197:13-56
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:201:13-203:29
action#android.service.notification.NotificationListenerService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:202:17-99
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:202:25-96
uses-sdk
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\be6bbc30661cacc9681da4493f1624bf\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\be6bbc30661cacc9681da4493f1624bf\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:autojs6-module] F:\work2025\shanghai\android-tool-v2\android-tool-v2\autojs6-module\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\b0a02ef2865f8b1ab6fcd0755a3cd915\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\b0a02ef2865f8b1ab6fcd0755a3cd915\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\f76b3476b820445629bb44fd97cdba1c\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\f76b3476b820445629bb44fd97cdba1c\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\35eea97605e8f8ac8a957559700c83be\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\35eea97605e8f8ac8a957559700c83be\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\bca5d58794b22aca6036379432e392dd\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\bca5d58794b22aca6036379432e392dd\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d8a17e80b34dfeac4d4324ceeb8814\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\40d8a17e80b34dfeac4d4324ceeb8814\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d96eae6c30f38e251d287505e529eac3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d96eae6c30f38e251d287505e529eac3\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\466bd753c0bc663f243f57f85dfa0cbd\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\466bd753c0bc663f243f57f85dfa0cbd\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e9069ecca228dca1e9503696b9c910da\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e9069ecca228dca1e9503696b9c910da\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b1b6d71ac725a2fc4ab91a425e45a1c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b1b6d71ac725a2fc4ab91a425e45a1c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\31e366bfae40842c9679e2edb5cfb3f7\transformed\safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\31e366bfae40842c9679e2edb5cfb3f7\transformed\safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\50cc86ed3c6197819ca1f984760c144b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\50cc86ed3c6197819ca1f984760c144b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\92658cbff1b755dc31e432292d3e02b3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\92658cbff1b755dc31e432292d3e02b3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f897afc1102d841ba1635c00b08f1354\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f897afc1102d841ba1635c00b08f1354\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\e88a25488b9f006132cae73aba61784a\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\e88a25488b9f006132cae73aba61784a\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\996035a061487a70b28bf5eee4e4e32d\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\996035a061487a70b28bf5eee4e4e32d\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\acf1eb6355a0aa51fff0c9886c913059\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\acf1eb6355a0aa51fff0c9886c913059\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ff88e707fd1ad5cea819bc2df513d9f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\8ff88e707fd1ad5cea819bc2df513d9f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\0c7728c0dbc7c5197515014cd79b6b95\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\0c7728c0dbc7c5197515014cd79b6b95\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\03ddd522988da343dbe79f2302dcd006\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\03ddd522988da343dbe79f2302dcd006\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\19059f77d33f7f3ee99aa46c6349a71c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\19059f77d33f7f3ee99aa46c6349a71c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\339ca94cdafe38a77bfd905e2704f20e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\339ca94cdafe38a77bfd905e2704f20e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\85a5a08400df38977c0ecf718ef11a2c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\85a5a08400df38977c0ecf718ef11a2c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\df523c34031b8b51fceccd8e6c5704d7\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\df523c34031b8b51fceccd8e6c5704d7\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\384cb12f5073881ae43b8877bf01735a\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\384cb12f5073881ae43b8877bf01735a\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\840970417fed3f773a2fe740b38bd53a\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\840970417fed3f773a2fe740b38bd53a\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fc62e522023f0ae3c150e3bb85b5180d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fc62e522023f0ae3c150e3bb85b5180d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f220744e8e6e74085c3f48ebfa7d5d45\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\f220744e8e6e74085c3f48ebfa7d5d45\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8f9e326ffde6c021ca8a6b9f49456b0f\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8f9e326ffde6c021ca8a6b9f49456b0f\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\0fb22fd98bfcf3852f353526066948a4\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\0fb22fd98bfcf3852f353526066948a4\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3905a59dd8d4010ee767d2164842210\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3905a59dd8d4010ee767d2164842210\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\483f913148bc9db67e36bb73b45ef125\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\483f913148bc9db67e36bb73b45ef125\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\65b9a7bc33494692978a1a522eeec93d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\65b9a7bc33494692978a1a522eeec93d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fb0fc75fdf2f5fe2613f79f3aca2fe6\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fb0fc75fdf2f5fe2613f79f3aca2fe6\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1d1b607e6712116afdd99f5448a1331e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1d1b607e6712116afdd99f5448a1331e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6231b9f97c8ae5d2272d84e6569ead21\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6231b9f97c8ae5d2272d84e6569ead21\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a07013b9d0389473114f29be64bf39f5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a07013b9d0389473114f29be64bf39f5\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\29e21a8ce910b658e4fb80a790e89f60\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\29e21a8ce910b658e4fb80a790e89f60\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\3fd97b68fd2cab07c2aede205d02f337\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\3fd97b68fd2cab07c2aede205d02f337\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e9781549dfe16481e4886d4f453fb678\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e9781549dfe16481e4886d4f453fb678\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6dac5ab0b5c19f4ba9824e6dbb25d749\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\6dac5ab0b5c19f4ba9824e6dbb25d749\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6d6bc14ce48f3b94204266a4fad77eaa\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\6d6bc14ce48f3b94204266a4fad77eaa\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\62999619145cd22b7b45bc443dd83ef4\transformed\FreeReflection-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\62999619145cd22b7b45bc443dd83ef4\transformed\FreeReflection-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\0db9fee98cb4986175685be692ee59e3\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\0db9fee98cb4986175685be692ee59e3\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e2a52dc5b09baccf98389115e69331b3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e2a52dc5b09baccf98389115e69331b3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f6cdcd292ac6ea0e1583eb98bbebf52\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f6cdcd292ac6ea0e1583eb98bbebf52\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0ec72d84172d3a6e9a28c1f087724aa7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0ec72d84172d3a6e9a28c1f087724aa7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\257f149158ae9891e31bf1007eee8575\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\257f149158ae9891e31bf1007eee8575\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c5afe4edb7ca63a225899e49783f60cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c5afe4edb7ca63a225899e49783f60cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c15531773577e3540f4a3e218e27c149\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c15531773577e3540f4a3e218e27c149\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\edbe1198ab35d6c14d19da5bfeac10e4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\edbe1198ab35d6c14d19da5bfeac10e4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\26b6109143b84f80832baac7b85c75a6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\26b6109143b84f80832baac7b85c75a6\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\94cf0955da1394be7fa131abcac7297f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\94cf0955da1394be7fa131abcac7297f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7407a8c36690a03b2cdbd44f46565f3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7407a8c36690a03b2cdbd44f46565f3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c5afe4edb7ca63a225899e49783f60cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c5afe4edb7ca63a225899e49783f60cc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
