package org.autojs.autojs.theme.app

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performInTransactionSuspending
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import javax.`annotation`.processing.Generated
import kotlin.Boolean
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class ColorHistoryDao_Impl(
  __db: RoomDatabase,
) : ColorHistoryDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfColorHistory: EntityInsertAdapter<ColorEntities.ColorHistory>

  private val __deleteAdapterOfColorHistory: EntityDeleteOrUpdateAdapter<ColorEntities.ColorHistory>

  private val __updateAdapterOfColorHistory: EntityDeleteOrUpdateAdapter<ColorEntities.ColorHistory>
  init {
    this.__db = __db
    this.__insertAdapterOfColorHistory = object : EntityInsertAdapter<ColorEntities.ColorHistory>()
        {
      protected override fun createQuery(): String =
          "INSERT OR IGNORE INTO `color_history` (`id`,`library_id`,`item_id`,`last_used_time`) VALUES (nullif(?, 0),?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: ColorEntities.ColorHistory) {
        statement.bindLong(1, entity.id.toLong())
        statement.bindLong(2, entity.libraryId.toLong())
        statement.bindLong(3, entity.itemId.toLong())
        statement.bindLong(4, entity.lastUsedTime)
      }
    }
    this.__deleteAdapterOfColorHistory = object :
        EntityDeleteOrUpdateAdapter<ColorEntities.ColorHistory>() {
      protected override fun createQuery(): String = "DELETE FROM `color_history` WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: ColorEntities.ColorHistory) {
        statement.bindLong(1, entity.id.toLong())
      }
    }
    this.__updateAdapterOfColorHistory = object :
        EntityDeleteOrUpdateAdapter<ColorEntities.ColorHistory>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `color_history` SET `id` = ?,`library_id` = ?,`item_id` = ?,`last_used_time` = ? WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: ColorEntities.ColorHistory) {
        statement.bindLong(1, entity.id.toLong())
        statement.bindLong(2, entity.libraryId.toLong())
        statement.bindLong(3, entity.itemId.toLong())
        statement.bindLong(4, entity.lastUsedTime)
        statement.bindLong(5, entity.id.toLong())
      }
    }
  }

  public override suspend fun insert(history: ColorEntities.ColorHistory): Long =
      performSuspending(__db, false, true) { _connection ->
    val _result: Long = __insertAdapterOfColorHistory.insertAndReturnId(_connection, history)
    _result
  }

  public override suspend fun delete(vararg histories: ColorEntities.ColorHistory): Unit =
      performSuspending(__db, false, true) { _connection ->
    __deleteAdapterOfColorHistory.handleMultiple(_connection, histories)
  }

  public override suspend fun update(history: ColorEntities.ColorHistory): Unit =
      performSuspending(__db, false, true) { _connection ->
    __updateAdapterOfColorHistory.handle(_connection, history)
  }

  public override suspend fun upsert(history: ColorEntities.ColorHistory): Long =
      performInTransactionSuspending(__db) {
    super@ColorHistoryDao_Impl.upsert(history)
  }

  public override suspend fun getByLibraryIdAndItemId(libraryId: Long, itemId: Long):
      ColorEntities.ColorHistory? {
    val _sql: String = "SELECT * FROM color_history WHERE library_id = ? AND item_id = ? LIMIT 1"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, libraryId)
        _argIndex = 2
        _stmt.bindLong(_argIndex, itemId)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfLibraryId: Int = getColumnIndexOrThrow(_stmt, "library_id")
        val _columnIndexOfItemId: Int = getColumnIndexOrThrow(_stmt, "item_id")
        val _columnIndexOfLastUsedTime: Int = getColumnIndexOrThrow(_stmt, "last_used_time")
        val _result: ColorEntities.ColorHistory?
        if (_stmt.step()) {
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpLibraryId: Int
          _tmpLibraryId = _stmt.getLong(_columnIndexOfLibraryId).toInt()
          val _tmpItemId: Int
          _tmpItemId = _stmt.getLong(_columnIndexOfItemId).toInt()
          val _tmpLastUsedTime: Long
          _tmpLastUsedTime = _stmt.getLong(_columnIndexOfLastUsedTime)
          _result = ColorEntities.ColorHistory(_tmpId,_tmpLibraryId,_tmpItemId,_tmpLastUsedTime)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getAll(): List<ColorEntities.ColorHistory> {
    val _sql: String = "SELECT * FROM color_history"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfLibraryId: Int = getColumnIndexOrThrow(_stmt, "library_id")
        val _columnIndexOfItemId: Int = getColumnIndexOrThrow(_stmt, "item_id")
        val _columnIndexOfLastUsedTime: Int = getColumnIndexOrThrow(_stmt, "last_used_time")
        val _result: MutableList<ColorEntities.ColorHistory> = mutableListOf()
        while (_stmt.step()) {
          val _item: ColorEntities.ColorHistory
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpLibraryId: Int
          _tmpLibraryId = _stmt.getLong(_columnIndexOfLibraryId).toInt()
          val _tmpItemId: Int
          _tmpItemId = _stmt.getLong(_columnIndexOfItemId).toInt()
          val _tmpLastUsedTime: Long
          _tmpLastUsedTime = _stmt.getLong(_columnIndexOfLastUsedTime)
          _item = ColorEntities.ColorHistory(_tmpId,_tmpLibraryId,_tmpItemId,_tmpLastUsedTime)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getAllByLibraryId(libraryId: Long): List<ColorEntities.ColorHistory> {
    val _sql: String = "SELECT * FROM color_history WHERE library_id = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, libraryId)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfLibraryId: Int = getColumnIndexOrThrow(_stmt, "library_id")
        val _columnIndexOfItemId: Int = getColumnIndexOrThrow(_stmt, "item_id")
        val _columnIndexOfLastUsedTime: Int = getColumnIndexOrThrow(_stmt, "last_used_time")
        val _result: MutableList<ColorEntities.ColorHistory> = mutableListOf()
        while (_stmt.step()) {
          val _item: ColorEntities.ColorHistory
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpLibraryId: Int
          _tmpLibraryId = _stmt.getLong(_columnIndexOfLibraryId).toInt()
          val _tmpItemId: Int
          _tmpItemId = _stmt.getLong(_columnIndexOfItemId).toInt()
          val _tmpLastUsedTime: Long
          _tmpLastUsedTime = _stmt.getLong(_columnIndexOfLastUsedTime)
          _item = ColorEntities.ColorHistory(_tmpId,_tmpLibraryId,_tmpItemId,_tmpLastUsedTime)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun hasData(): Boolean {
    val _sql: String = "SELECT EXISTS(SELECT 1 FROM color_history LIMIT 1)"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _result: Boolean
        if (_stmt.step()) {
          val _tmp: Int
          _tmp = _stmt.getLong(0).toInt()
          _result = _tmp != 0
        } else {
          _result = false
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun hasDataByLibraryId(libraryId: Long): Boolean {
    val _sql: String = "SELECT EXISTS(SELECT 1 FROM color_history WHERE library_id = ? LIMIT 1)"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, libraryId)
        val _result: Boolean
        if (_stmt.step()) {
          val _tmp: Int
          _tmp = _stmt.getLong(0).toInt()
          _result = _tmp != 0
        } else {
          _result = false
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAll() {
    val _sql: String = "DELETE FROM color_history"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAllByLibraryId(libraryId: Long) {
    val _sql: String = "DELETE FROM color_history WHERE library_id = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, libraryId)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
