// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.core.console.ConsoleView;
import org.autojs.autojs.theme.widget.ThemeColorFloatingActionButton;
import org.autojs.autojs.theme.widget.ThemeColorToolbar;
import org.autojs.autojs6.R;

public final class ActivityLogBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ConsoleView console;

  @NonNull
  public final ThemeColorFloatingActionButton fab;

  @NonNull
  public final ThemeColorToolbar toolbar;

  private ActivityLogBinding(@NonNull CoordinatorLayout rootView, @NonNull ConsoleView console,
      @NonNull ThemeColorFloatingActionButton fab, @NonNull ThemeColorToolbar toolbar) {
    this.rootView = rootView;
    this.console = console;
    this.fab = fab;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_log, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.console;
      ConsoleView console = ViewBindings.findChildViewById(rootView, id);
      if (console == null) {
        break missingId;
      }

      id = R.id.fab;
      ThemeColorFloatingActionButton fab = ViewBindings.findChildViewById(rootView, id);
      if (fab == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityLogBinding((CoordinatorLayout) rootView, console, fab, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
