// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.ui.widget.ViewSwitcher;
import org.autojs.autojs6.R;

public final class EfExpandableFloatyContainerBinding implements ViewBinding {
  @NonNull
  private final ViewSwitcher rootView;

  @NonNull
  public final ViewSwitcher container;

  private EfExpandableFloatyContainerBinding(@NonNull ViewSwitcher rootView,
      @NonNull ViewSwitcher container) {
    this.rootView = rootView;
    this.container = container;
  }

  @Override
  @NonNull
  public ViewSwitcher getRoot() {
    return rootView;
  }

  @NonNull
  public static EfExpandableFloatyContainerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static EfExpandableFloatyContainerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.ef_expandable_floaty_container, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static EfExpandableFloatyContainerBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    ViewSwitcher container = (ViewSwitcher) rootView;

    return new EfExpandableFloatyContainerBinding((ViewSwitcher) rootView, container);
  }
}
