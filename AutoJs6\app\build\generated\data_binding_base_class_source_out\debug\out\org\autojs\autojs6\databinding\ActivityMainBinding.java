// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.FragmentContainerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorFloatingActionButton;
import org.autojs.autojs.theme.widget.ThemeColorToolbar;
import org.autojs.autojs.ui.main.FloatingActionMenu;
import org.autojs.autojs.ui.pager.ScrollControllableViewPager;
import org.autojs.autojs6.R;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final AppBarLayout appBar;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final ThemeColorFloatingActionButton fab;

  @NonNull
  public final FloatingActionMenu floatingActionMenu;

  @NonNull
  public final FragmentContainerView fragmentDrawer;

  @NonNull
  public final TabLayout tab;

  @NonNull
  public final ThemeColorToolbar toolbar;

  @NonNull
  public final ScrollControllableViewPager viewpager;

  private ActivityMainBinding(@NonNull DrawerLayout rootView, @NonNull AppBarLayout appBar,
      @NonNull DrawerLayout drawerLayout, @NonNull ThemeColorFloatingActionButton fab,
      @NonNull FloatingActionMenu floatingActionMenu, @NonNull FragmentContainerView fragmentDrawer,
      @NonNull TabLayout tab, @NonNull ThemeColorToolbar toolbar,
      @NonNull ScrollControllableViewPager viewpager) {
    this.rootView = rootView;
    this.appBar = appBar;
    this.drawerLayout = drawerLayout;
    this.fab = fab;
    this.floatingActionMenu = floatingActionMenu;
    this.fragmentDrawer = fragmentDrawer;
    this.tab = tab;
    this.toolbar = toolbar;
    this.viewpager = viewpager;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar;
      AppBarLayout appBar = ViewBindings.findChildViewById(rootView, id);
      if (appBar == null) {
        break missingId;
      }

      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.fab;
      ThemeColorFloatingActionButton fab = ViewBindings.findChildViewById(rootView, id);
      if (fab == null) {
        break missingId;
      }

      id = R.id.floating_action_menu;
      FloatingActionMenu floatingActionMenu = ViewBindings.findChildViewById(rootView, id);
      if (floatingActionMenu == null) {
        break missingId;
      }

      id = R.id.fragment_drawer;
      FragmentContainerView fragmentDrawer = ViewBindings.findChildViewById(rootView, id);
      if (fragmentDrawer == null) {
        break missingId;
      }

      id = R.id.tab;
      TabLayout tab = ViewBindings.findChildViewById(rootView, id);
      if (tab == null) {
        break missingId;
      }

      id = R.id.toolbar;
      ThemeColorToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.viewpager;
      ScrollControllableViewPager viewpager = ViewBindings.findChildViewById(rootView, id);
      if (viewpager == null) {
        break missingId;
      }

      return new ActivityMainBinding((DrawerLayout) rootView, appBar, drawerLayout, fab,
          floatingActionMenu, fragmentDrawer, tab, toolbar, viewpager);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
