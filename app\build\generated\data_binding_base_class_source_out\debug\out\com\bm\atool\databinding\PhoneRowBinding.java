// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import com.bm.atool.ui.views.BlackUnderlineEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class PhoneRowBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final BlackUnderlineEditText edtPhoneNumber;

  @NonNull
  public final TextView txtDisplayName;

  @NonNull
  public final TextView txtSlot;

  @NonNull
  public final TextView txtSubscriptionId;

  private PhoneRowBinding(@NonNull LinearLayout rootView,
      @NonNull BlackUnderlineEditText edtPhoneNumber, @NonNull TextView txtDisplayName,
      @NonNull TextView txtSlot, @NonNull TextView txtSubscriptionId) {
    this.rootView = rootView;
    this.edtPhoneNumber = edtPhoneNumber;
    this.txtDisplayName = txtDisplayName;
    this.txtSlot = txtSlot;
    this.txtSubscriptionId = txtSubscriptionId;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static PhoneRowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PhoneRowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.phone_row, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PhoneRowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.edtPhoneNumber;
      BlackUnderlineEditText edtPhoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (edtPhoneNumber == null) {
        break missingId;
      }

      id = R.id.txtDisplayName;
      TextView txtDisplayName = ViewBindings.findChildViewById(rootView, id);
      if (txtDisplayName == null) {
        break missingId;
      }

      id = R.id.txtSlot;
      TextView txtSlot = ViewBindings.findChildViewById(rootView, id);
      if (txtSlot == null) {
        break missingId;
      }

      id = R.id.txtSubscriptionId;
      TextView txtSubscriptionId = ViewBindings.findChildViewById(rootView, id);
      if (txtSubscriptionId == null) {
        break missingId;
      }

      return new PhoneRowBinding((LinearLayout) rootView, edtPhoneNumber, txtDisplayName, txtSlot,
          txtSubscriptionId);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
