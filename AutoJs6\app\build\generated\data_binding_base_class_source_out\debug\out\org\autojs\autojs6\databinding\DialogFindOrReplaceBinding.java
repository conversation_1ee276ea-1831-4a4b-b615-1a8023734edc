// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorCheckBox;
import org.autojs.autojs6.R;

public final class DialogFindOrReplaceBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ThemeColorCheckBox checkboxRegex;

  @NonNull
  public final ThemeColorCheckBox checkboxReplace;

  @NonNull
  public final ThemeColorCheckBox checkboxReplaceAll;

  @NonNull
  public final AppCompatEditText keywords;

  @NonNull
  public final AppCompatEditText replacement;

  private DialogFindOrReplaceBinding(@NonNull LinearLayout rootView,
      @NonNull ThemeColorCheckBox checkboxRegex, @NonNull ThemeColorCheckBox checkboxReplace,
      @NonNull ThemeColorCheckBox checkboxReplaceAll, @NonNull AppCompatEditText keywords,
      @NonNull AppCompatEditText replacement) {
    this.rootView = rootView;
    this.checkboxRegex = checkboxRegex;
    this.checkboxReplace = checkboxReplace;
    this.checkboxReplaceAll = checkboxReplaceAll;
    this.keywords = keywords;
    this.replacement = replacement;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogFindOrReplaceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogFindOrReplaceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_find_or_replace, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogFindOrReplaceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkbox_regex;
      ThemeColorCheckBox checkboxRegex = ViewBindings.findChildViewById(rootView, id);
      if (checkboxRegex == null) {
        break missingId;
      }

      id = R.id.checkbox_replace;
      ThemeColorCheckBox checkboxReplace = ViewBindings.findChildViewById(rootView, id);
      if (checkboxReplace == null) {
        break missingId;
      }

      id = R.id.checkbox_replace_all;
      ThemeColorCheckBox checkboxReplaceAll = ViewBindings.findChildViewById(rootView, id);
      if (checkboxReplaceAll == null) {
        break missingId;
      }

      id = R.id.keywords;
      AppCompatEditText keywords = ViewBindings.findChildViewById(rootView, id);
      if (keywords == null) {
        break missingId;
      }

      id = R.id.replacement;
      AppCompatEditText replacement = ViewBindings.findChildViewById(rootView, id);
      if (replacement == null) {
        break missingId;
      }

      return new DialogFindOrReplaceBinding((LinearLayout) rootView, checkboxRegex, checkboxReplace,
          checkboxReplaceAll, keywords, replacement);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
