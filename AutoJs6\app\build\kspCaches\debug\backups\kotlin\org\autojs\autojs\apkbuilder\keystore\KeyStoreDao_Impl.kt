package org.autojs.autojs.apkbuilder.keystore

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.EntityUpsertAdapter
import androidx.room.RoomDatabase
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.getTotalChangedRows
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import javax.`annotation`.processing.Generated
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class KeyStoreDao_Impl(
  __db: RoomDatabase,
) : KeyStoreDao {
  private val __db: RoomDatabase

  private val __deleteAdapterOfKeyStore: EntityDeleteOrUpdateAdapter<KeyStore>

  private val __upsertAdapterOfKeyStore: EntityUpsertAdapter<KeyStore>
  init {
    this.__db = __db
    this.__deleteAdapterOfKeyStore = object : EntityDeleteOrUpdateAdapter<KeyStore>() {
      protected override fun createQuery(): String =
          "DELETE FROM `KeyStore` WHERE `absolutePath` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: KeyStore) {
        statement.bindText(1, entity.absolutePath)
      }
    }
    this.__upsertAdapterOfKeyStore = EntityUpsertAdapter<KeyStore>(object :
        EntityInsertAdapter<KeyStore>() {
      protected override fun createQuery(): String =
          "INSERT INTO `KeyStore` (`absolutePath`,`filename`,`password`,`alias`,`alias_password`,`verified`) VALUES (?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: KeyStore) {
        statement.bindText(1, entity.absolutePath)
        statement.bindText(2, entity.filename)
        statement.bindText(3, entity.password)
        statement.bindText(4, entity.alias)
        statement.bindText(5, entity.aliasPassword)
        val _tmp: Int = if (entity.verified) 1 else 0
        statement.bindLong(6, _tmp.toLong())
      }
    }, object : EntityDeleteOrUpdateAdapter<KeyStore>() {
      protected override fun createQuery(): String =
          "UPDATE `KeyStore` SET `absolutePath` = ?,`filename` = ?,`password` = ?,`alias` = ?,`alias_password` = ?,`verified` = ? WHERE `absolutePath` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: KeyStore) {
        statement.bindText(1, entity.absolutePath)
        statement.bindText(2, entity.filename)
        statement.bindText(3, entity.password)
        statement.bindText(4, entity.alias)
        statement.bindText(5, entity.aliasPassword)
        val _tmp: Int = if (entity.verified) 1 else 0
        statement.bindLong(6, _tmp.toLong())
        statement.bindText(7, entity.absolutePath)
      }
    })
  }

  public override suspend fun delete(vararg keyStores: KeyStore): Unit = performSuspending(__db,
      false, true) { _connection ->
    __deleteAdapterOfKeyStore.handleMultiple(_connection, keyStores)
  }

  public override suspend fun upsert(vararg keyStores: KeyStore): Unit = performSuspending(__db,
      false, true) { _connection ->
    __upsertAdapterOfKeyStore.upsert(_connection, keyStores)
  }

  public override suspend fun getByAbsolutePath(absolutePath: String): KeyStore? {
    val _sql: String = "SELECT * FROM keystore WHERE absolutePath = ? LIMIT 1"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, absolutePath)
        val _columnIndexOfAbsolutePath: Int = getColumnIndexOrThrow(_stmt, "absolutePath")
        val _columnIndexOfFilename: Int = getColumnIndexOrThrow(_stmt, "filename")
        val _columnIndexOfPassword: Int = getColumnIndexOrThrow(_stmt, "password")
        val _columnIndexOfAlias: Int = getColumnIndexOrThrow(_stmt, "alias")
        val _columnIndexOfAliasPassword: Int = getColumnIndexOrThrow(_stmt, "alias_password")
        val _columnIndexOfVerified: Int = getColumnIndexOrThrow(_stmt, "verified")
        val _result: KeyStore?
        if (_stmt.step()) {
          val _tmpAbsolutePath: String
          _tmpAbsolutePath = _stmt.getText(_columnIndexOfAbsolutePath)
          val _tmpFilename: String
          _tmpFilename = _stmt.getText(_columnIndexOfFilename)
          val _tmpPassword: String
          _tmpPassword = _stmt.getText(_columnIndexOfPassword)
          val _tmpAlias: String
          _tmpAlias = _stmt.getText(_columnIndexOfAlias)
          val _tmpAliasPassword: String
          _tmpAliasPassword = _stmt.getText(_columnIndexOfAliasPassword)
          val _tmpVerified: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfVerified).toInt()
          _tmpVerified = _tmp != 0
          _result =
              KeyStore(_tmpAbsolutePath,_tmpFilename,_tmpPassword,_tmpAlias,_tmpAliasPassword,_tmpVerified)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getAll(): List<KeyStore> {
    val _sql: String = "SELECT * FROM keystore"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfAbsolutePath: Int = getColumnIndexOrThrow(_stmt, "absolutePath")
        val _columnIndexOfFilename: Int = getColumnIndexOrThrow(_stmt, "filename")
        val _columnIndexOfPassword: Int = getColumnIndexOrThrow(_stmt, "password")
        val _columnIndexOfAlias: Int = getColumnIndexOrThrow(_stmt, "alias")
        val _columnIndexOfAliasPassword: Int = getColumnIndexOrThrow(_stmt, "alias_password")
        val _columnIndexOfVerified: Int = getColumnIndexOrThrow(_stmt, "verified")
        val _result: MutableList<KeyStore> = mutableListOf()
        while (_stmt.step()) {
          val _item: KeyStore
          val _tmpAbsolutePath: String
          _tmpAbsolutePath = _stmt.getText(_columnIndexOfAbsolutePath)
          val _tmpFilename: String
          _tmpFilename = _stmt.getText(_columnIndexOfFilename)
          val _tmpPassword: String
          _tmpPassword = _stmt.getText(_columnIndexOfPassword)
          val _tmpAlias: String
          _tmpAlias = _stmt.getText(_columnIndexOfAlias)
          val _tmpAliasPassword: String
          _tmpAliasPassword = _stmt.getText(_columnIndexOfAliasPassword)
          val _tmpVerified: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfVerified).toInt()
          _tmpVerified = _tmp != 0
          _item =
              KeyStore(_tmpAbsolutePath,_tmpFilename,_tmpPassword,_tmpAlias,_tmpAliasPassword,_tmpVerified)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteByAbsolutePath(absolutePath: String): Int {
    val _sql: String = "DELETE FROM keystore WHERE absolutePath = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, absolutePath)
        _stmt.step()
        getTotalChangedRows(_connection)
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAll() {
    val _sql: String = "DELETE FROM keystore"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
