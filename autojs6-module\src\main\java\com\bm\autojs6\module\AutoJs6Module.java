package com.bm.autojs6.module;

import android.content.Context;
import android.util.Log;

import com.bm.autojs6.module.engine.ScriptEngine;
import com.bm.autojs6.module.model.Script;
import com.bm.autojs6.module.model.ScriptResult;

import java.util.concurrent.CompletableFuture;

/**
 * AutoJs6 Module - 简化的AutoJs6集成模块
 * 提供脚本执行、管理等核心功能
 */
public class AutoJs6Module {
    private static final String TAG = "AutoJs6Module";
    
    private static volatile AutoJs6Module instance;
    private Context context;
    private ScriptEngine scriptEngine;
    private boolean isInitialized = false;
    
    private AutoJs6Module() {
        // Private constructor for singleton
    }
    
    /**
     * 获取单例实例
     */
    public static AutoJs6Module getInstance() {
        if (instance == null) {
            synchronized (AutoJs6Module.class) {
                if (instance == null) {
                    instance = new AutoJs6Module();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化模块
     */
    public void initialize(Context context) {
        if (isInitialized) {
            Log.w(TAG, "AutoJs6Module already initialized");
            return;
        }
        
        this.context = context.getApplicationContext();
        this.scriptEngine = new ScriptEngine(this.context);
        this.isInitialized = true;
        
        Log.i(TAG, "AutoJs6Module initialized successfully");
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 同步执行脚本
     */
    public ScriptResult executeScript(Script script) {
        if (!isInitialized) {
            throw new IllegalStateException("AutoJs6Module not initialized");
        }
        
        return scriptEngine.execute(script);
    }
    
    /**
     * 异步执行脚本
     */
    public CompletableFuture<ScriptResult> executeScriptAsync(Script script) {
        if (!isInitialized) {
            CompletableFuture<ScriptResult> future = new CompletableFuture<>();
            future.completeExceptionally(new IllegalStateException("AutoJs6Module not initialized"));
            return future;
        }
        
        return CompletableFuture.supplyAsync(() -> scriptEngine.execute(script));
    }
    
    /**
     * 停止所有正在运行的脚本
     */
    public void stopAllScripts() {
        if (scriptEngine != null) {
            scriptEngine.stopAll();
        }
    }
    
    /**
     * 获取正在运行的脚本数量
     */
    public int getRunningScriptCount() {
        if (scriptEngine != null) {
            return scriptEngine.getRunningCount();
        }
        return 0;
    }
    
    /**
     * 释放资源
     */
    public void shutdown() {
        if (scriptEngine != null) {
            scriptEngine.shutdown();
        }
        isInitialized = false;
        Log.i(TAG, "AutoJs6Module shutdown");
    }
}
