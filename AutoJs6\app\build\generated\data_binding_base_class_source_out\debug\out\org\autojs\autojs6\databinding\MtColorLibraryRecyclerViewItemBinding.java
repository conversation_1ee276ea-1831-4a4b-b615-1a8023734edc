// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class MtColorLibraryRecyclerViewItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView color;

  @NonNull
  public final TextView colorLibraryIdentifier;

  @NonNull
  public final TextView description;

  @NonNull
  public final TextView name;

  @NonNull
  public final TextView subtitleSplitLine;

  private MtColorLibraryRecyclerViewItemBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView color, @NonNull TextView colorLibraryIdentifier,
      @NonNull TextView description, @NonNull TextView name, @NonNull TextView subtitleSplitLine) {
    this.rootView = rootView;
    this.color = color;
    this.colorLibraryIdentifier = colorLibraryIdentifier;
    this.description = description;
    this.name = name;
    this.subtitleSplitLine = subtitleSplitLine;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MtColorLibraryRecyclerViewItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MtColorLibraryRecyclerViewItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.mt_color_library_recycler_view_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MtColorLibraryRecyclerViewItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.color;
      ImageView color = ViewBindings.findChildViewById(rootView, id);
      if (color == null) {
        break missingId;
      }

      id = R.id.color_library_identifier;
      TextView colorLibraryIdentifier = ViewBindings.findChildViewById(rootView, id);
      if (colorLibraryIdentifier == null) {
        break missingId;
      }

      id = R.id.description;
      TextView description = ViewBindings.findChildViewById(rootView, id);
      if (description == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.subtitle_split_line;
      TextView subtitleSplitLine = ViewBindings.findChildViewById(rootView, id);
      if (subtitleSplitLine == null) {
        break missingId;
      }

      return new MtColorLibraryRecyclerViewItemBinding((LinearLayout) rootView, color,
          colorLibraryIdentifier, description, name, subtitleSplitLine);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
