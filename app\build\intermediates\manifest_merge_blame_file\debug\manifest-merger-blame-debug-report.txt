1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bm.atool"
4    android:versionCode="1"
5    android:versionName="1.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.telephony"
12-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:6:9-50
13        android:required="false" />
13-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-33
14
15    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
15-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:9:5-10:47
15-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:9:22-83
16    <uses-permission android:name="android.permission.RECEIVE_SMS" />
16-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:11:5-70
16-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:11:22-67
17    <uses-permission android:name="android.permission.READ_SMS" />
17-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-67
17-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-64
18    <uses-permission android:name="android.permission.SEND_SMS" />
18-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
18-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
19    <uses-permission android:name="android.permission.WRITE_SMS" />
19-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-68
19-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-65
20    <uses-permission android:name="android.permission.INTERNET" />
20-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-67
20-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-64
21    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
21-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-75
21-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-72
22    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
22-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-76
22-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-77
23-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-68
24-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-65
25
26    <permission android:name="android.permission.DEVICE_POWER" />
26-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-21:51
26-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:17-63
27
28    <uses-permission android:name="android.permission.BIND_JOB_SERVICE" />
28-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:22:5-23:47
28-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:22:22-72
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:24:5-79
29-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:24:22-76
30    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
30-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
30-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-76
31-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-73
32    <uses-permission android:name="android.permission.INTERNET" />
32-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-67
32-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-64
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:28:5-80
33-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:28:22-77
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-77
34-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-74
35    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES" />
35-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
35-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
36    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
36-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-79
36-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-76
37    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
37-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-81
37-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-78
38    <uses-permission android:name="android.permission.INTERNET" />
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-67
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-64
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:34:5-92
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:34:22-89
40    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-95
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-92
41    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
41-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-77
41-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-75
42    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
42-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-38:47
42-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-82
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:39:5-80
43-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:39:22-78
44
45    <!-- AutoJs6 additional permissions -->
46    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
46-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:42:5-81
46-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:42:22-78
47    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
47-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:5-44:40
47-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:22-79
48    <uses-permission android:name="android.permission.VIBRATE" />
48-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:45:5-66
48-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:45:22-63
49    <uses-permission android:name="android.permission.CAMERA" />
49-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:5-65
49-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:22-62
50    <uses-permission android:name="android.permission.RECORD_AUDIO" />
50-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:5-71
50-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:22-68
51    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
51-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:5-49:47
51-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:22-76
52    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
52-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:50:5-51:47
52-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:50:22-83
53    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
53-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:52:5-94
53-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:52:22-91
54    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
54-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:5-89
54-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:22-86
55
56    <permission
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
57        android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:5-222:19
63        android:name="com.bm.atool.App"
63-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:64:9-28
64        android:allowBackup="true"
64-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:55:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\cee19163c10f1fafd81d90a045427d43\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
66        android:dataExtractionRules="@xml/data_extraction_rules"
66-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:56:9-65
67        android:debuggable="true"
68        android:extractNativeLibs="false"
69        android:fullBackupContent="@xml/backup_rules"
69-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:57:9-54
70        android:icon="@mipmap/ic_launcher"
70-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:58:9-43
71        android:label="@string/app_name"
71-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:59:9-41
72        android:roundIcon="@mipmap/ic_launcher_round"
72-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:60:9-54
73        android:supportsRtl="true"
73-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:61:9-35
74        android:testOnly="true"
75        android:theme="@style/Theme.AndroidTool"
75-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:62:9-49
76        android:usesCleartextTraffic="true" >
76-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:63:9-44
77        <activity
77-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:67:9-69:40
78            android:name="com.bm.atool.LoginActivity"
78-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:68:13-42
79            android:exported="false" />
79-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:69:13-37
80        <activity
80-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:70:9-83:20
81            android:name="com.bm.atool.MainActivity"
81-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:71:13-41
82            android:excludeFromRecents="true"
82-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-46
83            android:exported="true"
83-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-36
84            android:label="@string/app_name"
84-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:73:13-45
85            android:launchMode="singleTask"
85-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-44
86            android:taskAffinity=""
86-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-36
87            android:theme="@style/Theme.AndroidTool" >
87-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:74:13-53
88            <intent-filter>
88-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:78:13-82:29
89                <action android:name="android.intent.action.MAIN" />
89-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:79:17-69
89-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:79:25-66
90
91                <category android:name="android.intent.category.LAUNCHER" />
91-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:81:17-77
91-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:81:27-74
92            </intent-filter>
93        </activity>
94        <activity
94-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:84:9-86:39
95            android:name="com.bm.atool.service.singlepixel.SinglePixelActivity"
95-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:85:13-68
96            android:exported="true" />
96-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:86:13-36
97
98        <receiver
98-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:88:9-94:20
99            android:name="com.bm.atool.receivers.SimChangedReceiver"
99-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:89:13-57
100            android:exported="true" >
100-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-36
101            <intent-filter>
101-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-93:29
102                <action android:name="android.intent.action.SIM_STATE_CHANGED" />
102-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:92:17-81
102-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:92:25-79
103            </intent-filter>
104        </receiver>
105        <receiver
105-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:9-101:20
106            android:name="com.bm.atool.receivers.SmsReceiver"
106-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:95:19-56
107            android:exported="true"
107-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:13-36
108            android:permission="android.permission.BROADCAST_SMS" >
108-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:97:13-66
109            <intent-filter>
109-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:13-100:29
110                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
110-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:17-81
110-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:25-79
111            </intent-filter>
112        </receiver>
113        <receiver
113-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:103:9-116:20
114            android:name="com.bm.atool.receivers.WakeUpReceiver"
114-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:104:13-53
115            android:exported="true"
115-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-36
116            android:process=":watch" >
116-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-37
117            <intent-filter>
117-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:107:13-115:29
118                <action android:name="android.intent.action.USER_PRESENT" />
118-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:17-76
118-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:25-74
119                <action android:name="android.intent.action.BOOT_COMPLETED" />
119-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
119-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
120                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
120-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
120-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
121                <action android:name="android.intent.action.USER_PRESENT" />
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:17-76
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:25-74
122                <action android:name="android.intent.action.MEDIA_MOUNTED" />
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:112:17-78
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:112:25-75
123                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
123-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:17-87
123-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:25-84
124                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
124-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-90
124-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-87
125            </intent-filter>
126        </receiver>
127        <receiver
127-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:117:9-144:20
128            android:name="com.bm.atool.receivers.WakeUpAutoStartReceiver"
128-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:118:13-62
129            android:exported="true"
129-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:120:13-36
130            android:process=":watch" >
130-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-37
131
132            <!-- 手机启动 -->
133            <intent-filter>
133-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:122:13-125:29
134                <action android:name="android.intent.action.BOOT_COMPLETED" />
134-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
134-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
135                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
135-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
135-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
136            </intent-filter>
137            <!-- 软件安装卸载 -->
138            <intent-filter>
138-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:127:13-131:29
139                <action android:name="android.intent.action.PACKAGE_ADDED" />
139-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:128:17-77
139-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:128:25-75
140                <action android:name="android.intent.action.PACKAGE_REMOVED" />
140-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:17-79
140-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:25-77
141
142                <data android:scheme="package" />
142-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
142-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
143            </intent-filter>
144            <!-- 网络监听 -->
145            <intent-filter>
145-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:133:13-137:29
146                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
146-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
146-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
147                <action android:name="android.net.wifi.WIFI_STATE_CJANGED" />
147-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:135:17-77
147-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:135:25-75
148                <action android:name="android.net.wifi.STATE_CHANGE" />
148-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:17-71
148-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:25-69
149            </intent-filter>
150            <!-- 文件挂载 -->
151            <intent-filter>
151-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:139:13-143:29
152                <action android:name="android.intent.action.MEDIA_EJECT" />
152-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140:17-75
152-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140:25-73
153                <action android:name="android.intent.action.MEDIA_MOUNTED" />
153-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:112:17-78
153-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:112:25-75
154
155                <data android:scheme="file" />
155-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
155-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
156            </intent-filter>
157        </receiver>
158
159        <!-- 守护进程 watch -->
160        <service
160-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:147:9-152:43
161            android:name="com.bm.atool.service.JobSchedulerService"
161-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:148:13-56
162            android:enabled="true"
162-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:150:13-35
163            android:exported="true"
163-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:151:13-36
164            android:permission="android.permission.BIND_JOB_SERVICE"
164-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-69
165            android:process=":watch_job" />
165-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:152:13-41
166        <service
166-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:154:9-159:43
167            android:name="com.bm.atool.service.WatchDogService"
167-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:155:13-52
168            android:enabled="true"
168-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:157:13-35
169            android:exported="true"
169-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:158:13-36
170            android:foregroundServiceType="mediaPlayback"
170-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:156:13-58
171            android:process=":watch_dog" />
171-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:159:13-41
172        <service
172-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:161:9-163:46
173            android:name="com.bm.atool.service.PlayMusicService"
173-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:161:18-58
174            android:foregroundServiceType="mediaPlayback"
174-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:162:13-58
175            android:process=":watch_player" />
175-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:163:13-44
176        <service
176-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:164:9-177:19
177            android:name="com.bm.atool.service.ANTAccessibilityService"
177-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:165:13-60
178            android:enabled="true"
178-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:166:13-35
179            android:exported="true"
179-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:167:13-36
180            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
180-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-79
181            android:process=":accessibility" >
181-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-45
182            <intent-filter>
182-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-172:29
183                <action android:name="android.accessibilityservice.AccessibilityService" />
183-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:17-92
183-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:25-89
184            </intent-filter>
185
186            <meta-data
186-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:174:13-176:54
187                android:name="android.accessibilityservice"
187-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:175:17-60
188                android:resource="@xml/allocation" />
188-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:176:17-51
189        </service>
190        <service
190-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:178:9-194:19
191            android:name="com.bm.atool.service.SocketService"
191-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:179:13-50
192            android:exported="false"
192-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:180:13-37
193            android:label="SocketService"
193-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:181:13-42
194            android:permission="android.permission.BIND_VPN_SERVICE"
194-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-69
195            android:process=":socket" >
195-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:182:13-38
196            <intent-filter>
196-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:184:13-186:29
197                <action android:name="android.net.VpnService" />
197-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:185:17-65
197-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:185:25-62
198            </intent-filter>
199
200            <meta-data
200-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:187:13-189:39
201                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
201-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:188:17-73
202                android:value="true" />
202-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:189:17-37
203
204            <property
204-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:190:13-192:38
205                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
205-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:191:17-76
206                android:value="vpn" />
206-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:192:17-36
207        </service>
208        <service
208-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:196:9-204:19
209            android:name="com.bm.atool.service.NotificationService"
209-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:197:13-56
210            android:exported="true"
210-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:200:13-36
211            android:label="@string/app_name"
211-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:198:13-45
212            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
212-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:199:13-87
213            <intent-filter>
213-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:201:13-203:29
214                <action android:name="android.service.notification.NotificationListenerService" />
214-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:202:17-99
214-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:202:25-96
215            </intent-filter>
216        </service>
217
218        <!-- AutoJs6 Accessibility Service - 暂时注释掉 -->
219        <!--
220        <service
221            android:name=".service.AutoJs6AccessibilityService"
222            android:enabled="true"
223            android:exported="true"
224            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
225            <intent-filter>
226                <action android:name="android.accessibilityservice.AccessibilityService" />
227            </intent-filter>
228            <meta-data
229                android:name="android.accessibilityservice"
230                android:resource="@xml/autojs_accessibility_service" />
231        </service>
232        -->
233
234        <provider
234-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
235            android:name="androidx.startup.InitializationProvider"
235-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
236            android:authorities="com.bm.atool.androidx-startup"
236-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
237            android:exported="false" >
237-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
238            <meta-data
238-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
239                android:name="androidx.emoji2.text.EmojiCompatInitializer"
239-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
240                android:value="androidx.startup" />
240-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\731b34b02142770d6a2658cc8d5707c5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
241            <meta-data
241-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
242                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
242-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
243                android:value="androidx.startup" />
243-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f4e35635ec18953ad4e61f1b36fdf55\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
244            <meta-data
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
245                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
246                android:value="androidx.startup" />
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
247        </provider>
248
249        <uses-library
249-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
250            android:name="androidx.window.extensions"
250-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
251            android:required="false" />
251-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
252        <uses-library
252-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
253            android:name="androidx.window.sidecar"
253-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
254            android:required="false" />
254-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b86d1003ef8f467021703b254a3b550a\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
255
256        <receiver
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
257            android:name="androidx.profileinstaller.ProfileInstallReceiver"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
258            android:directBootAware="false"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
259            android:enabled="true"
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
260            android:exported="true"
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
261            android:permission="android.permission.DUMP" >
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
263                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
266                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
269                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
270            </intent-filter>
271            <intent-filter>
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
272                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\77a6410275f88068d6975713cf75e91a\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
273            </intent-filter>
274        </receiver>
275    </application>
276
277</manifest>
