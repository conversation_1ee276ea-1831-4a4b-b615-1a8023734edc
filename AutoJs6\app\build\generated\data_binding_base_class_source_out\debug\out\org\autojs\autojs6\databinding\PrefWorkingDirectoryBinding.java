// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorRadioButton;
import org.autojs.autojs.theme.widget.ThemeColorSwitch;
import org.autojs.autojs6.R;

public final class PrefWorkingDirectoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayoutCompat rootView;

  @NonNull
  public final ThemeColorRadioButton copy;

  @NonNull
  public final LinearLayoutCompat mdContentFileTransfer;

  @NonNull
  public final EditText mdContentPath;

  @NonNull
  public final RadioGroup mdContentRadioGroup;

  @NonNull
  public final ThemeColorSwitch mdContentSwitch;

  @NonNull
  public final ImageView mdFileChooserIcon;

  @NonNull
  public final ThemeColorRadioButton move;

  private PrefWorkingDirectoryBinding(@NonNull LinearLayoutCompat rootView,
      @NonNull ThemeColorRadioButton copy, @NonNull LinearLayoutCompat mdContentFileTransfer,
      @NonNull EditText mdContentPath, @NonNull RadioGroup mdContentRadioGroup,
      @NonNull ThemeColorSwitch mdContentSwitch, @NonNull ImageView mdFileChooserIcon,
      @NonNull ThemeColorRadioButton move) {
    this.rootView = rootView;
    this.copy = copy;
    this.mdContentFileTransfer = mdContentFileTransfer;
    this.mdContentPath = mdContentPath;
    this.mdContentRadioGroup = mdContentRadioGroup;
    this.mdContentSwitch = mdContentSwitch;
    this.mdFileChooserIcon = mdFileChooserIcon;
    this.move = move;
  }

  @Override
  @NonNull
  public LinearLayoutCompat getRoot() {
    return rootView;
  }

  @NonNull
  public static PrefWorkingDirectoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static PrefWorkingDirectoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.pref_working_directory, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static PrefWorkingDirectoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.copy;
      ThemeColorRadioButton copy = ViewBindings.findChildViewById(rootView, id);
      if (copy == null) {
        break missingId;
      }

      id = R.id.md_contentFileTransfer;
      LinearLayoutCompat mdContentFileTransfer = ViewBindings.findChildViewById(rootView, id);
      if (mdContentFileTransfer == null) {
        break missingId;
      }

      id = R.id.md_contentPath;
      EditText mdContentPath = ViewBindings.findChildViewById(rootView, id);
      if (mdContentPath == null) {
        break missingId;
      }

      id = R.id.md_contentRadioGroup;
      RadioGroup mdContentRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (mdContentRadioGroup == null) {
        break missingId;
      }

      id = R.id.md_contentSwitch;
      ThemeColorSwitch mdContentSwitch = ViewBindings.findChildViewById(rootView, id);
      if (mdContentSwitch == null) {
        break missingId;
      }

      id = R.id.md_FileChooserIcon;
      ImageView mdFileChooserIcon = ViewBindings.findChildViewById(rootView, id);
      if (mdFileChooserIcon == null) {
        break missingId;
      }

      id = R.id.move;
      ThemeColorRadioButton move = ViewBindings.findChildViewById(rootView, id);
      if (move == null) {
        break missingId;
      }

      return new PrefWorkingDirectoryBinding((LinearLayoutCompat) rootView, copy,
          mdContentFileTransfer, mdContentPath, mdContentRadioGroup, mdContentSwitch,
          mdFileChooserIcon, move);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
