// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.explorer.ExplorerProjectToolbar;
import org.autojs.autojs6.R;

public final class ExplorerViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView explorerItemList;

  @NonNull
  public final ExplorerProjectToolbar projectToolbar;

  private ExplorerViewBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView explorerItemList, @NonNull ExplorerProjectToolbar projectToolbar) {
    this.rootView = rootView;
    this.explorerItemList = explorerItemList;
    this.projectToolbar = projectToolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ExplorerViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExplorerViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.explorer_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExplorerViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.explorer_item_list;
      RecyclerView explorerItemList = ViewBindings.findChildViewById(rootView, id);
      if (explorerItemList == null) {
        break missingId;
      }

      id = R.id.project_toolbar;
      ExplorerProjectToolbar projectToolbar = ViewBindings.findChildViewById(rootView, id);
      if (projectToolbar == null) {
        break missingId;
      }

      return new ExplorerViewBinding((LinearLayout) rootView, explorerItemList, projectToolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
