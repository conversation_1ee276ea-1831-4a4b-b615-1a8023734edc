plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.devtools.ksp'
//    id 'stringfog'
   // id "com.google.protobuf"
//    id 'com.squareup.wire'
}
apply plugin: 'stringfog'

// 导入RandomKeyGenerator类，如果使用HardCodeKeyGenerator，更换下类名
import com.github.megatronking.stringfog.plugin.kg.RandomKeyGenerator
import com.github.megatronking.stringfog.plugin.StringFogMode

//sourceSets {
//    main.java.srcDirs += "$buildDir/generated/source/wire"
//}

stringfog {

    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.25'

    // 必要：加解密库的实现类路径，需和上面配置的加解密算法库一致。
    implementation 'com.github.megatronking.stringfog.xor.StringFogImpl'
    // 可选：StringFog会自动尝试获取packageName，如果遇到获取失败的情况，可以显式地指定。
    packageName 'com.github.megatronking.stringfog.app'
    // 可选：加密开关，默认开启。
    enable true
    // 可选：指定需加密的代码包路径，可配置多个，未指定将默认全部加密。
//    fogPackages = ['com.bm.atool']
    // 可选（3.0版本新增）：指定密钥生成器，默认使用长度8的随机密钥（每个字符串均有不同随机密钥）,
    // 也可以指定一个固定的密钥：HardCodeKeyGenerator("This is a key")
    kg new RandomKeyGenerator()
    // 可选（4.0版本新增）：用于控制字符串加密后在字节码中的存在形式, 默认为base64，
    // 也可以使用text或者bytes
    mode com.github.megatronking.stringfog.plugin.StringFogMode.bytes
}
android {
    namespace 'com.bm.atool'
    compileSdk 35

    defaultConfig {
        applicationId 'com.bm.atool'
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // Manifest placeholders for AutoJs6
        manifestPlaceholders = [
            icon: '@mipmap/ic_launcher',
            appName: '@string/app_name',
            CHANNEL: 'default',
            intentCategory: 'android.intent.category.LAUNCHER',
            authorities: 'com.bm.atool.fileprovider',
            intentCategoryInrt: 'android.intent.category.DEFAULT'
        ]
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    buildFeatures {
        viewBinding true
        buildConfig = true
    }
    buildToolsVersion '34'

    applicationVariants.all { variant ->
        variant.outputs.all {
            def date = new java.text.SimpleDateFormat("yyyyMMdd_HHmm").format(new java.util.Date())
            outputFileName = "SMSTool_v${variant.versionName}_${date}.apk"
        }
    }

}
dependencies {

    // Core library desugaring
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'

    // AutoJs6 Module - 简化的AutoJs6集成模块
    implementation project(':autojs6-module')

    // AutoJs6 integration - 启用AutoJS 6依赖 (暂时禁用，等待修复兼容性问题)
    // implementation project(':AutoJs6')

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.navigation:navigation-fragment:2.7.7'
    implementation 'androidx.navigation:navigation-ui:2.7.7'

    implementation 'com.google.code.gson:gson:2.11.0'
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
//    implementation 'com.squareup.retrofit2:adapter-rxjava2:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.google.android.material:material:1.2.1'

    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.25'

    implementation ('io.socket:socket.io-client:2.1.1') {
        exclude group: 'org.json', module: 'json'
    }
    // https://mvnrepository.com/artifact/org.javassist/javassist
    implementation("org.javassist:javassist:3.28.0-GA")
    implementation("io.reactivex.rxjava2:rxjava:2.+")

//    implementation ':droidguasso:@jar'
//    implementation files('libs/droidguasso.jar')
//    implementation "com.squareup.wire:wire-runtime:$wireVersion"
    implementation "org.microg:safe-parcel:$safeParcelVersion"
//    implementation 'com.google.protobuf:protobuf-java:4.28.0-RC2'
  //  implementation 'com.google.protobuf:protobuf-lite:3.0.1'

    implementation 'androidx.preference:preference:1.2.0'
    implementation 'androidx.lifecycle:lifecycle-service:2.4.1'
    implementation 'com.tananaev:adblib:1.3'
    implementation 'commons-codec:commons-codec:1.3'


//    implementation project(':remote-droid-guard')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    
    // 添加UI Automator依赖
    androidTestImplementation 'androidx.test.uiautomator:uiautomator:2.2.0'
    
    // 添加测试相关依赖
    androidTestImplementation 'androidx.test:core:1.5.0'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    
    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.8.22"))

    // Kotlin support
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"
    implementation "org.jetbrains.kotlin:kotlin-reflect"

    // MultiDex support for AutoJs6
    implementation 'androidx.multidex:multidex:2.0.1'

    implementation 'com.github.tiann:FreeReflection:3.2.2'
    implementation 'com.github.megatronking.stringfog:xor:5.0.0'


}
