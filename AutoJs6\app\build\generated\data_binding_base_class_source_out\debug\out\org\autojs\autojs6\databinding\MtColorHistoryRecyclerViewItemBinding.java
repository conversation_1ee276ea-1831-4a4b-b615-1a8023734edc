// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class MtColorHistoryRecyclerViewItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView color;

  @NonNull
  public final TextView colorHex;

  @NonNull
  public final TextView colorLibraryIdentifier;

  @NonNull
  public final TextView colorName;

  @NonNull
  public final TextView lastUsedTime;

  @NonNull
  public final TextView subtitleSplitLine;

  @NonNull
  public final TextView titleSplitLine;

  private MtColorHistoryRecyclerViewItemBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView color, @NonNull TextView colorHex,
      @NonNull TextView colorLibraryIdentifier, @NonNull TextView colorName,
      @NonNull TextView lastUsedTime, @NonNull TextView subtitleSplitLine,
      @NonNull TextView titleSplitLine) {
    this.rootView = rootView;
    this.color = color;
    this.colorHex = colorHex;
    this.colorLibraryIdentifier = colorLibraryIdentifier;
    this.colorName = colorName;
    this.lastUsedTime = lastUsedTime;
    this.subtitleSplitLine = subtitleSplitLine;
    this.titleSplitLine = titleSplitLine;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MtColorHistoryRecyclerViewItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MtColorHistoryRecyclerViewItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.mt_color_history_recycler_view_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MtColorHistoryRecyclerViewItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.color;
      ImageView color = ViewBindings.findChildViewById(rootView, id);
      if (color == null) {
        break missingId;
      }

      id = R.id.color_hex;
      TextView colorHex = ViewBindings.findChildViewById(rootView, id);
      if (colorHex == null) {
        break missingId;
      }

      id = R.id.color_library_identifier;
      TextView colorLibraryIdentifier = ViewBindings.findChildViewById(rootView, id);
      if (colorLibraryIdentifier == null) {
        break missingId;
      }

      id = R.id.color_name;
      TextView colorName = ViewBindings.findChildViewById(rootView, id);
      if (colorName == null) {
        break missingId;
      }

      id = R.id.last_used_time;
      TextView lastUsedTime = ViewBindings.findChildViewById(rootView, id);
      if (lastUsedTime == null) {
        break missingId;
      }

      id = R.id.subtitle_split_line;
      TextView subtitleSplitLine = ViewBindings.findChildViewById(rootView, id);
      if (subtitleSplitLine == null) {
        break missingId;
      }

      id = R.id.title_split_line;
      TextView titleSplitLine = ViewBindings.findChildViewById(rootView, id);
      if (titleSplitLine == null) {
        break missingId;
      }

      return new MtColorHistoryRecyclerViewItemBinding((LinearLayout) rootView, color, colorHex,
          colorLibraryIdentifier, colorName, lastUsedTime, subtitleSplitLine, titleSplitLine);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
