// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.core.ui.widget.JsCheckBox;
import org.autojs.autojs6.R;

public final class FileChooseListFileBinding implements ViewBinding {
  @NonNull
  private final LinearLayoutCompat rootView;

  @NonNull
  public final JsCheckBox checkbox;

  @NonNull
  public final LinearLayout item;

  @NonNull
  public final TextView name;

  @NonNull
  public final TextView scriptFileDate;

  @NonNull
  public final TextView scriptFileSize;

  private FileChooseListFileBinding(@NonNull LinearLayoutCompat rootView,
      @NonNull JsCheckBox checkbox, @NonNull LinearLayout item, @NonNull TextView name,
      @NonNull TextView scriptFileDate, @NonNull TextView scriptFileSize) {
    this.rootView = rootView;
    this.checkbox = checkbox;
    this.item = item;
    this.name = name;
    this.scriptFileDate = scriptFileDate;
    this.scriptFileSize = scriptFileSize;
  }

  @Override
  @NonNull
  public LinearLayoutCompat getRoot() {
    return rootView;
  }

  @NonNull
  public static FileChooseListFileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FileChooseListFileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.file_choose_list_file, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FileChooseListFileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkbox;
      JsCheckBox checkbox = ViewBindings.findChildViewById(rootView, id);
      if (checkbox == null) {
        break missingId;
      }

      id = R.id.item;
      LinearLayout item = ViewBindings.findChildViewById(rootView, id);
      if (item == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.script_file_date;
      TextView scriptFileDate = ViewBindings.findChildViewById(rootView, id);
      if (scriptFileDate == null) {
        break missingId;
      }

      id = R.id.script_file_size;
      TextView scriptFileSize = ViewBindings.findChildViewById(rootView, id);
      if (scriptFileSize == null) {
        break missingId;
      }

      return new FileChooseListFileBinding((LinearLayoutCompat) rootView, checkbox, item, name,
          scriptFileDate, scriptFileSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
