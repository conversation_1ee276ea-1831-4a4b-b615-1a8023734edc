/*
 * This file is auto-generated.  DO NOT MODIFY.
 */
package org.autojs.autojs.core.shizuku;
public interface IUserService extends android.os.IInterface
{
  /** Default implementation for IUserService. */
  public static class Default implements org.autojs.autojs.core.shizuku.IUserService
  {
    @Override public void destroy() throws android.os.RemoteException
    {
    }
    // Destroy method defined by Shizuku server
    @Override public void exit() throws android.os.RemoteException
    {
    }
    // Exit method defined by user
    @Override public java.lang.String execCommand(java.lang.String command) throws android.os.RemoteException
    {
      return null;
    }
    @Override public java.lang.String currentPackage() throws android.os.RemoteException
    {
      return null;
    }
    @Override public java.lang.String currentActivity() throws android.os.RemoteException
    {
      return null;
    }
    @Override public java.lang.String currentComponent() throws android.os.RemoteException
    {
      return null;
    }
    @Override public java.lang.String currentComponentShort() throws android.os.RemoteException
    {
      return null;
    }
    @Override
    public android.os.IBinder asBinder() {
      return null;
    }
  }
  /** Local-side IPC implementation stub class. */
  public static abstract class Stub extends android.os.Binder implements org.autojs.autojs.core.shizuku.IUserService
  {
    /** Construct the stub at attach it to the interface. */
    public Stub()
    {
      this.attachInterface(this, DESCRIPTOR);
    }
    /**
     * Cast an IBinder object into an org.autojs.autojs.core.shizuku.IUserService interface,
     * generating a proxy if needed.
     */
    public static org.autojs.autojs.core.shizuku.IUserService asInterface(android.os.IBinder obj)
    {
      if ((obj==null)) {
        return null;
      }
      android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
      if (((iin!=null)&&(iin instanceof org.autojs.autojs.core.shizuku.IUserService))) {
        return ((org.autojs.autojs.core.shizuku.IUserService)iin);
      }
      return new org.autojs.autojs.core.shizuku.IUserService.Stub.Proxy(obj);
    }
    @Override public android.os.IBinder asBinder()
    {
      return this;
    }
    @Override public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws android.os.RemoteException
    {
      java.lang.String descriptor = DESCRIPTOR;
      if (code >= android.os.IBinder.FIRST_CALL_TRANSACTION && code <= android.os.IBinder.LAST_CALL_TRANSACTION) {
        data.enforceInterface(descriptor);
      }
      switch (code)
      {
        case INTERFACE_TRANSACTION:
        {
          reply.writeString(descriptor);
          return true;
        }
      }
      switch (code)
      {
        case TRANSACTION_destroy:
        {
          this.destroy();
          reply.writeNoException();
          break;
        }
        case TRANSACTION_exit:
        {
          this.exit();
          reply.writeNoException();
          break;
        }
        case TRANSACTION_execCommand:
        {
          java.lang.String _arg0;
          _arg0 = data.readString();
          java.lang.String _result = this.execCommand(_arg0);
          reply.writeNoException();
          reply.writeString(_result);
          break;
        }
        case TRANSACTION_currentPackage:
        {
          java.lang.String _result = this.currentPackage();
          reply.writeNoException();
          reply.writeString(_result);
          break;
        }
        case TRANSACTION_currentActivity:
        {
          java.lang.String _result = this.currentActivity();
          reply.writeNoException();
          reply.writeString(_result);
          break;
        }
        case TRANSACTION_currentComponent:
        {
          java.lang.String _result = this.currentComponent();
          reply.writeNoException();
          reply.writeString(_result);
          break;
        }
        case TRANSACTION_currentComponentShort:
        {
          java.lang.String _result = this.currentComponentShort();
          reply.writeNoException();
          reply.writeString(_result);
          break;
        }
        default:
        {
          return super.onTransact(code, data, reply, flags);
        }
      }
      return true;
    }
    private static class Proxy implements org.autojs.autojs.core.shizuku.IUserService
    {
      private android.os.IBinder mRemote;
      Proxy(android.os.IBinder remote)
      {
        mRemote = remote;
      }
      @Override public android.os.IBinder asBinder()
      {
        return mRemote;
      }
      public java.lang.String getInterfaceDescriptor()
      {
        return DESCRIPTOR;
      }
      @Override public void destroy() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_destroy, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      // Destroy method defined by Shizuku server
      @Override public void exit() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_exit, _data, _reply, 0);
          _reply.readException();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
      }
      // Exit method defined by user
      @Override public java.lang.String execCommand(java.lang.String command) throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          _data.writeString(command);
          boolean _status = mRemote.transact(Stub.TRANSACTION_execCommand, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public java.lang.String currentPackage() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_currentPackage, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public java.lang.String currentActivity() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_currentActivity, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public java.lang.String currentComponent() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_currentComponent, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
      @Override public java.lang.String currentComponentShort() throws android.os.RemoteException
      {
        android.os.Parcel _data = android.os.Parcel.obtain();
        android.os.Parcel _reply = android.os.Parcel.obtain();
        java.lang.String _result;
        try {
          _data.writeInterfaceToken(DESCRIPTOR);
          boolean _status = mRemote.transact(Stub.TRANSACTION_currentComponentShort, _data, _reply, 0);
          _reply.readException();
          _result = _reply.readString();
        }
        finally {
          _reply.recycle();
          _data.recycle();
        }
        return _result;
      }
    }
    static final int TRANSACTION_destroy = (android.os.IBinder.FIRST_CALL_TRANSACTION + 16777114);
    static final int TRANSACTION_exit = (android.os.IBinder.FIRST_CALL_TRANSACTION + 1);
    static final int TRANSACTION_execCommand = (android.os.IBinder.FIRST_CALL_TRANSACTION + 2);
    static final int TRANSACTION_currentPackage = (android.os.IBinder.FIRST_CALL_TRANSACTION + 11);
    static final int TRANSACTION_currentActivity = (android.os.IBinder.FIRST_CALL_TRANSACTION + 12);
    static final int TRANSACTION_currentComponent = (android.os.IBinder.FIRST_CALL_TRANSACTION + 13);
    static final int TRANSACTION_currentComponentShort = (android.os.IBinder.FIRST_CALL_TRANSACTION + 14);
  }
  public static final java.lang.String DESCRIPTOR = "org.autojs.autojs.core.shizuku.IUserService";
  public void destroy() throws android.os.RemoteException;
  // Destroy method defined by Shizuku server
  public void exit() throws android.os.RemoteException;
  // Exit method defined by user
  public java.lang.String execCommand(java.lang.String command) throws android.os.RemoteException;
  public java.lang.String currentPackage() throws android.os.RemoteException;
  public java.lang.String currentActivity() throws android.os.RemoteException;
  public java.lang.String currentComponent() throws android.os.RemoteException;
  public java.lang.String currentComponentShort() throws android.os.RemoteException;
}
