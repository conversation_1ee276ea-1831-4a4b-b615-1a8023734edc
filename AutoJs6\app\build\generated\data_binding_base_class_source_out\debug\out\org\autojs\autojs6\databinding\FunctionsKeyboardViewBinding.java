// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class FunctionsKeyboardViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView moduleList;

  @NonNull
  public final RecyclerView properties;

  private FunctionsKeyboardViewBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView moduleList, @NonNull RecyclerView properties) {
    this.rootView = rootView;
    this.moduleList = moduleList;
    this.properties = properties;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FunctionsKeyboardViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FunctionsKeyboardViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.functions_keyboard_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FunctionsKeyboardViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.module_list;
      RecyclerView moduleList = ViewBindings.findChildViewById(rootView, id);
      if (moduleList == null) {
        break missingId;
      }

      id = R.id.properties;
      RecyclerView properties = ViewBindings.findChildViewById(rootView, id);
      if (properties == null) {
        break missingId;
      }

      return new FunctionsKeyboardViewBinding((LinearLayout) rootView, moduleList, properties);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
