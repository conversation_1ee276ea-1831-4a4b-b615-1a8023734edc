// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.core.ui.widget.JsCheckBox;
import org.autojs.autojs6.R;

public final class FileChooseListDirectoryBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final JsCheckBox checkbox;

  @NonNull
  public final ImageView icon;

  @NonNull
  public final CardView item;

  @NonNull
  public final TextView name;

  private FileChooseListDirectoryBinding(@NonNull CardView rootView, @NonNull JsCheckBox checkbox,
      @NonNull ImageView icon, @NonNull CardView item, @NonNull TextView name) {
    this.rootView = rootView;
    this.checkbox = checkbox;
    this.icon = icon;
    this.item = item;
    this.name = name;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static FileChooseListDirectoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FileChooseListDirectoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.file_choose_list_directory, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FileChooseListDirectoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.checkbox;
      JsCheckBox checkbox = ViewBindings.findChildViewById(rootView, id);
      if (checkbox == null) {
        break missingId;
      }

      id = R.id.icon;
      ImageView icon = ViewBindings.findChildViewById(rootView, id);
      if (icon == null) {
        break missingId;
      }

      CardView item = (CardView) rootView;

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      return new FileChooseListDirectoryBinding((CardView) rootView, checkbox, icon, item, name);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
