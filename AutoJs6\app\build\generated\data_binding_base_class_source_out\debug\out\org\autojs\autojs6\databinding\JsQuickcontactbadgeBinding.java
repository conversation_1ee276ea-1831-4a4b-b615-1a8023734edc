// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.core.ui.widget.JsQuickContactBadge;
import org.autojs.autojs6.R;

public final class JsQuickcontactbadgeBinding implements ViewBinding {
  @NonNull
  private final JsQuickContactBadge rootView;

  private JsQuickcontactbadgeBinding(@NonNull JsQuickContactBadge rootView) {
    this.rootView = rootView;
  }

  @Override
  @NonNull
  public JsQuickContactBadge getRoot() {
    return rootView;
  }

  @NonNull
  public static JsQuickcontactbadgeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static JsQuickcontactbadgeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.js_quickcontactbadge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static JsQuickcontactbadgeBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    return new JsQuickcontactbadgeBinding((JsQuickContactBadge) rootView);
  }
}
