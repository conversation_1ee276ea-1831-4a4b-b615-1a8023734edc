// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class SelectLauncherShortcutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout launcherShortcutDocs;

  @NonNull
  public final LinearLayout launcherShortcutLog;

  @NonNull
  public final LinearLayout launcherShortcutSettings;

  private SelectLauncherShortcutBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout launcherShortcutDocs, @NonNull LinearLayout launcherShortcutLog,
      @NonNull LinearLayout launcherShortcutSettings) {
    this.rootView = rootView;
    this.launcherShortcutDocs = launcherShortcutDocs;
    this.launcherShortcutLog = launcherShortcutLog;
    this.launcherShortcutSettings = launcherShortcutSettings;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SelectLauncherShortcutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SelectLauncherShortcutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.select_launcher_shortcut, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SelectLauncherShortcutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.launcher_shortcut_docs;
      LinearLayout launcherShortcutDocs = ViewBindings.findChildViewById(rootView, id);
      if (launcherShortcutDocs == null) {
        break missingId;
      }

      id = R.id.launcher_shortcut_log;
      LinearLayout launcherShortcutLog = ViewBindings.findChildViewById(rootView, id);
      if (launcherShortcutLog == null) {
        break missingId;
      }

      id = R.id.launcher_shortcut_settings;
      LinearLayout launcherShortcutSettings = ViewBindings.findChildViewById(rootView, id);
      if (launcherShortcutSettings == null) {
        break missingId;
      }

      return new SelectLauncherShortcutBinding((LinearLayout) rootView, launcherShortcutDocs,
          launcherShortcutLog, launcherShortcutSettings);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
