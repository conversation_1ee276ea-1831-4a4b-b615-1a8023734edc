// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ExplorerDirectoryBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView icon;

  @NonNull
  public final CardView item;

  @NonNull
  public final ImageView more;

  @NonNull
  public final TextView name;

  @NonNull
  public final TextView scriptDirDate;

  private ExplorerDirectoryBinding(@NonNull CardView rootView, @NonNull ImageView icon,
      @NonNull CardView item, @NonNull ImageView more, @NonNull TextView name,
      @NonNull TextView scriptDirDate) {
    this.rootView = rootView;
    this.icon = icon;
    this.item = item;
    this.more = more;
    this.name = name;
    this.scriptDirDate = scriptDirDate;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ExplorerDirectoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExplorerDirectoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.explorer_directory, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExplorerDirectoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.icon;
      ImageView icon = ViewBindings.findChildViewById(rootView, id);
      if (icon == null) {
        break missingId;
      }

      CardView item = (CardView) rootView;

      id = R.id.more;
      ImageView more = ViewBindings.findChildViewById(rootView, id);
      if (more == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.script_dir_date;
      TextView scriptDirDate = ViewBindings.findChildViewById(rootView, id);
      if (scriptDirDate == null) {
        break missingId;
      }

      return new ExplorerDirectoryBinding((CardView) rootView, icon, item, more, name,
          scriptDirDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
