// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ColorInfoDialogListItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView colorHexColon;

  @NonNull
  public final Guideline colorHexGuideline;

  @NonNull
  public final TextView colorHexLabel;

  @NonNull
  public final ConstraintLayout colorHexParent;

  @NonNull
  public final TextView colorHexValue;

  @NonNull
  public final TextView colorHslColon;

  @NonNull
  public final Guideline colorHslGuideline;

  @NonNull
  public final TextView colorHslLabel;

  @NonNull
  public final ConstraintLayout colorHslParent;

  @NonNull
  public final TextView colorHslValue;

  @NonNull
  public final TextView colorHsvColon;

  @NonNull
  public final Guideline colorHsvGuideline;

  @NonNull
  public final TextView colorHsvLabel;

  @NonNull
  public final ConstraintLayout colorHsvParent;

  @NonNull
  public final TextView colorHsvValue;

  @NonNull
  public final TextView colorIntColon;

  @NonNull
  public final Guideline colorIntGuideline;

  @NonNull
  public final TextView colorIntLabel;

  @NonNull
  public final ConstraintLayout colorIntParent;

  @NonNull
  public final TextView colorIntValue;

  @NonNull
  public final TextView colorRgbColon;

  @NonNull
  public final Guideline colorRgbGuideline;

  @NonNull
  public final TextView colorRgbLabel;

  @NonNull
  public final ConstraintLayout colorRgbParent;

  @NonNull
  public final TextView colorRgbValue;

  private ColorInfoDialogListItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView colorHexColon, @NonNull Guideline colorHexGuideline,
      @NonNull TextView colorHexLabel, @NonNull ConstraintLayout colorHexParent,
      @NonNull TextView colorHexValue, @NonNull TextView colorHslColon,
      @NonNull Guideline colorHslGuideline, @NonNull TextView colorHslLabel,
      @NonNull ConstraintLayout colorHslParent, @NonNull TextView colorHslValue,
      @NonNull TextView colorHsvColon, @NonNull Guideline colorHsvGuideline,
      @NonNull TextView colorHsvLabel, @NonNull ConstraintLayout colorHsvParent,
      @NonNull TextView colorHsvValue, @NonNull TextView colorIntColon,
      @NonNull Guideline colorIntGuideline, @NonNull TextView colorIntLabel,
      @NonNull ConstraintLayout colorIntParent, @NonNull TextView colorIntValue,
      @NonNull TextView colorRgbColon, @NonNull Guideline colorRgbGuideline,
      @NonNull TextView colorRgbLabel, @NonNull ConstraintLayout colorRgbParent,
      @NonNull TextView colorRgbValue) {
    this.rootView = rootView;
    this.colorHexColon = colorHexColon;
    this.colorHexGuideline = colorHexGuideline;
    this.colorHexLabel = colorHexLabel;
    this.colorHexParent = colorHexParent;
    this.colorHexValue = colorHexValue;
    this.colorHslColon = colorHslColon;
    this.colorHslGuideline = colorHslGuideline;
    this.colorHslLabel = colorHslLabel;
    this.colorHslParent = colorHslParent;
    this.colorHslValue = colorHslValue;
    this.colorHsvColon = colorHsvColon;
    this.colorHsvGuideline = colorHsvGuideline;
    this.colorHsvLabel = colorHsvLabel;
    this.colorHsvParent = colorHsvParent;
    this.colorHsvValue = colorHsvValue;
    this.colorIntColon = colorIntColon;
    this.colorIntGuideline = colorIntGuideline;
    this.colorIntLabel = colorIntLabel;
    this.colorIntParent = colorIntParent;
    this.colorIntValue = colorIntValue;
    this.colorRgbColon = colorRgbColon;
    this.colorRgbGuideline = colorRgbGuideline;
    this.colorRgbLabel = colorRgbLabel;
    this.colorRgbParent = colorRgbParent;
    this.colorRgbValue = colorRgbValue;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ColorInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ColorInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.color_info_dialog_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ColorInfoDialogListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.color_hex_colon;
      TextView colorHexColon = ViewBindings.findChildViewById(rootView, id);
      if (colorHexColon == null) {
        break missingId;
      }

      id = R.id.color_hex_guideline;
      Guideline colorHexGuideline = ViewBindings.findChildViewById(rootView, id);
      if (colorHexGuideline == null) {
        break missingId;
      }

      id = R.id.color_hex_label;
      TextView colorHexLabel = ViewBindings.findChildViewById(rootView, id);
      if (colorHexLabel == null) {
        break missingId;
      }

      id = R.id.color_hex_parent;
      ConstraintLayout colorHexParent = ViewBindings.findChildViewById(rootView, id);
      if (colorHexParent == null) {
        break missingId;
      }

      id = R.id.color_hex_value;
      TextView colorHexValue = ViewBindings.findChildViewById(rootView, id);
      if (colorHexValue == null) {
        break missingId;
      }

      id = R.id.color_hsl_colon;
      TextView colorHslColon = ViewBindings.findChildViewById(rootView, id);
      if (colorHslColon == null) {
        break missingId;
      }

      id = R.id.color_hsl_guideline;
      Guideline colorHslGuideline = ViewBindings.findChildViewById(rootView, id);
      if (colorHslGuideline == null) {
        break missingId;
      }

      id = R.id.color_hsl_label;
      TextView colorHslLabel = ViewBindings.findChildViewById(rootView, id);
      if (colorHslLabel == null) {
        break missingId;
      }

      id = R.id.color_hsl_parent;
      ConstraintLayout colorHslParent = ViewBindings.findChildViewById(rootView, id);
      if (colorHslParent == null) {
        break missingId;
      }

      id = R.id.color_hsl_value;
      TextView colorHslValue = ViewBindings.findChildViewById(rootView, id);
      if (colorHslValue == null) {
        break missingId;
      }

      id = R.id.color_hsv_colon;
      TextView colorHsvColon = ViewBindings.findChildViewById(rootView, id);
      if (colorHsvColon == null) {
        break missingId;
      }

      id = R.id.color_hsv_guideline;
      Guideline colorHsvGuideline = ViewBindings.findChildViewById(rootView, id);
      if (colorHsvGuideline == null) {
        break missingId;
      }

      id = R.id.color_hsv_label;
      TextView colorHsvLabel = ViewBindings.findChildViewById(rootView, id);
      if (colorHsvLabel == null) {
        break missingId;
      }

      id = R.id.color_hsv_parent;
      ConstraintLayout colorHsvParent = ViewBindings.findChildViewById(rootView, id);
      if (colorHsvParent == null) {
        break missingId;
      }

      id = R.id.color_hsv_value;
      TextView colorHsvValue = ViewBindings.findChildViewById(rootView, id);
      if (colorHsvValue == null) {
        break missingId;
      }

      id = R.id.color_int_colon;
      TextView colorIntColon = ViewBindings.findChildViewById(rootView, id);
      if (colorIntColon == null) {
        break missingId;
      }

      id = R.id.color_int_guideline;
      Guideline colorIntGuideline = ViewBindings.findChildViewById(rootView, id);
      if (colorIntGuideline == null) {
        break missingId;
      }

      id = R.id.color_int_label;
      TextView colorIntLabel = ViewBindings.findChildViewById(rootView, id);
      if (colorIntLabel == null) {
        break missingId;
      }

      id = R.id.color_int_parent;
      ConstraintLayout colorIntParent = ViewBindings.findChildViewById(rootView, id);
      if (colorIntParent == null) {
        break missingId;
      }

      id = R.id.color_int_value;
      TextView colorIntValue = ViewBindings.findChildViewById(rootView, id);
      if (colorIntValue == null) {
        break missingId;
      }

      id = R.id.color_rgb_colon;
      TextView colorRgbColon = ViewBindings.findChildViewById(rootView, id);
      if (colorRgbColon == null) {
        break missingId;
      }

      id = R.id.color_rgb_guideline;
      Guideline colorRgbGuideline = ViewBindings.findChildViewById(rootView, id);
      if (colorRgbGuideline == null) {
        break missingId;
      }

      id = R.id.color_rgb_label;
      TextView colorRgbLabel = ViewBindings.findChildViewById(rootView, id);
      if (colorRgbLabel == null) {
        break missingId;
      }

      id = R.id.color_rgb_parent;
      ConstraintLayout colorRgbParent = ViewBindings.findChildViewById(rootView, id);
      if (colorRgbParent == null) {
        break missingId;
      }

      id = R.id.color_rgb_value;
      TextView colorRgbValue = ViewBindings.findChildViewById(rootView, id);
      if (colorRgbValue == null) {
        break missingId;
      }

      return new ColorInfoDialogListItemBinding((ConstraintLayout) rootView, colorHexColon,
          colorHexGuideline, colorHexLabel, colorHexParent, colorHexValue, colorHslColon,
          colorHslGuideline, colorHslLabel, colorHslParent, colorHslValue, colorHsvColon,
          colorHsvGuideline, colorHsvLabel, colorHsvParent, colorHsvValue, colorIntColon,
          colorIntGuideline, colorIntLabel, colorIntParent, colorIntValue, colorRgbColon,
          colorRgbGuideline, colorRgbLabel, colorRgbParent, colorRgbValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
