// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import io.codetail.widget.RevealFrameLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.app.ColorSettingRecyclerView;
import org.autojs.autojs.ui.widget.AdaptiveTitleToolbar;
import org.autojs.autojs6.R;

public final class MtActivityColorSelectBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppBarLayout appBar;

  @NonNull
  public final RevealFrameLayout appBarContainer;

  @NonNull
  public final ColorSettingRecyclerView colorSettingRecyclerView;

  @NonNull
  public final AdaptiveTitleToolbar toolbar;

  private MtActivityColorSelectBinding(@NonNull LinearLayout rootView, @NonNull AppBarLayout appBar,
      @NonNull RevealFrameLayout appBarContainer,
      @NonNull ColorSettingRecyclerView colorSettingRecyclerView,
      @NonNull AdaptiveTitleToolbar toolbar) {
    this.rootView = rootView;
    this.appBar = appBar;
    this.appBarContainer = appBarContainer;
    this.colorSettingRecyclerView = colorSettingRecyclerView;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MtActivityColorSelectBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MtActivityColorSelectBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.mt_activity_color_select, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MtActivityColorSelectBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar;
      AppBarLayout appBar = ViewBindings.findChildViewById(rootView, id);
      if (appBar == null) {
        break missingId;
      }

      id = R.id.appBarContainer;
      RevealFrameLayout appBarContainer = ViewBindings.findChildViewById(rootView, id);
      if (appBarContainer == null) {
        break missingId;
      }

      id = R.id.color_setting_recycler_view;
      ColorSettingRecyclerView colorSettingRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (colorSettingRecyclerView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      AdaptiveTitleToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new MtActivityColorSelectBinding((LinearLayout) rootView, appBar, appBarContainer,
          colorSettingRecyclerView, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
