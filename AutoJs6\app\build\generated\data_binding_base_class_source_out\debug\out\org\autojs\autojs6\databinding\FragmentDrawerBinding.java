// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class FragmentDrawerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView drawerMenu;

  @NonNull
  public final LinearLayout drawerMenuContainer;

  @NonNull
  public final LinearLayout exit;

  @NonNull
  public final LinearLayout restart;

  @NonNull
  public final LinearLayout settings;

  private FragmentDrawerBinding(@NonNull LinearLayout rootView, @NonNull RecyclerView drawerMenu,
      @NonNull LinearLayout drawerMenuContainer, @NonNull LinearLayout exit,
      @NonNull LinearLayout restart, @NonNull LinearLayout settings) {
    this.rootView = rootView;
    this.drawerMenu = drawerMenu;
    this.drawerMenuContainer = drawerMenuContainer;
    this.exit = exit;
    this.restart = restart;
    this.settings = settings;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDrawerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDrawerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_drawer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDrawerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.drawer_menu;
      RecyclerView drawerMenu = ViewBindings.findChildViewById(rootView, id);
      if (drawerMenu == null) {
        break missingId;
      }

      LinearLayout drawerMenuContainer = (LinearLayout) rootView;

      id = R.id.exit;
      LinearLayout exit = ViewBindings.findChildViewById(rootView, id);
      if (exit == null) {
        break missingId;
      }

      id = R.id.restart;
      LinearLayout restart = ViewBindings.findChildViewById(rootView, id);
      if (restart == null) {
        break missingId;
      }

      id = R.id.settings;
      LinearLayout settings = ViewBindings.findChildViewById(rootView, id);
      if (settings == null) {
        break missingId;
      }

      return new FragmentDrawerBinding((LinearLayout) rootView, drawerMenu, drawerMenuContainer,
          exit, restart, settings);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
