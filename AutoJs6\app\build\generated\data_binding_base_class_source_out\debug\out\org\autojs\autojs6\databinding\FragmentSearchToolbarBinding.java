// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.widget.ToolbarMenuItem;
import org.autojs.autojs6.R;

public final class FragmentSearchToolbarBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ToolbarMenuItem cancelSearch;

  @NonNull
  public final ToolbarMenuItem findNext;

  @NonNull
  public final ToolbarMenuItem findPrev;

  @NonNull
  public final ToolbarMenuItem replace;

  private FragmentSearchToolbarBinding(@NonNull LinearLayout rootView,
      @NonNull ToolbarMenuItem cancelSearch, @NonNull ToolbarMenuItem findNext,
      @NonNull ToolbarMenuItem findPrev, @NonNull ToolbarMenuItem replace) {
    this.rootView = rootView;
    this.cancelSearch = cancelSearch;
    this.findNext = findNext;
    this.findPrev = findPrev;
    this.replace = replace;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSearchToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSearchToolbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_search_toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSearchToolbarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cancel_search;
      ToolbarMenuItem cancelSearch = ViewBindings.findChildViewById(rootView, id);
      if (cancelSearch == null) {
        break missingId;
      }

      id = R.id.find_next;
      ToolbarMenuItem findNext = ViewBindings.findChildViewById(rootView, id);
      if (findNext == null) {
        break missingId;
      }

      id = R.id.find_prev;
      ToolbarMenuItem findPrev = ViewBindings.findChildViewById(rootView, id);
      if (findPrev == null) {
        break missingId;
      }

      id = R.id.replace;
      ToolbarMenuItem replace = ViewBindings.findChildViewById(rootView, id);
      if (replace == null) {
        break missingId;
      }

      return new FragmentSearchToolbarBinding((LinearLayout) rootView, cancelSearch, findNext,
          findPrev, replace);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
