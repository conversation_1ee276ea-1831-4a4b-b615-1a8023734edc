<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets"><file name="autojs.keystore" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\autojs.keystore"/><file name="binary/root_automator" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\binary\root_automator"/><file name="default_key_store.bks" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\default_key_store.bks"/><file name="init.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\init.js"/><file name="modules/array-observe.min.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\array-observe.min.js"/><file name="modules/axios/axios.min.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\axios.min.js"/><file name="modules/axios/browser-libs/Blob.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\browser-libs\Blob.js"/><file name="modules/axios/browser-libs/Event.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\browser-libs\Event.js"/><file name="modules/axios/browser-libs/EventTarget.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\browser-libs\EventTarget.js"/><file name="modules/axios/browser-libs/FormData.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\browser-libs\FormData.js"/><file name="modules/axios/browser-libs/index.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\browser-libs\index.js"/><file name="modules/axios/browser-libs/utils.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\browser-libs\utils.js"/><file name="modules/axios/browser-libs/XMLHttpRequest.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\browser-libs\XMLHttpRequest.js"/><file name="modules/axios/remark.md" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\remark.md"/><file name="modules/axios/utils/index.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\utils\index.js"/><file name="modules/axios/utils/ThreadPool.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios\utils\ThreadPool.js"/><file name="modules/axios.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\axios.js"/><file name="modules/banana-i18n.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\banana-i18n.js"/><file name="modules/cheerio.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\cheerio.js"/><file name="modules/continuation.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\continuation.js"/><file name="modules/dayjs/dayjs.min.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\dayjs\dayjs.min.js"/><file name="modules/dayjs/index.d.ts" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\dayjs\index.d.ts"/><file name="modules/dayjs/LICENSE" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\dayjs\LICENSE"/><file name="modules/dayjs/locale.json" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\dayjs\locale.json"/><file name="modules/dayjs/package.json" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\dayjs\package.json"/><file name="modules/dayjs/README.md" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\dayjs\README.md"/><file name="modules/dayjs.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\dayjs.js"/><file name="modules/i18n.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\i18n.js"/><file name="modules/internal.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\internal.js"/><file name="modules/jvm-npm.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\jvm-npm.js"/><file name="modules/lodash.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\lodash.js"/><file name="modules/object-observe-lite.min.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\object-observe-lite.min.js"/><file name="modules/promise.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\promise.js"/><file name="modules/result-adapter.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\result-adapter.js"/><file name="modules/result_adapter.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\result_adapter.js"/><file name="modules/ui-ext.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\modules\ui-ext.js"/><file name="web/dist/autojs.sdk.v1.js" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\web\dist\autojs.sdk.v1.js"/><file name="web/dist/<EMAIL>" path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\main\assets\web\dist\<EMAIL>"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="F:\work2025\shanghai\android-tool-v2\android-tool-v2\AutoJs6\app\build\intermediates\shader_assets\debug\out"/></dataSet></merger>