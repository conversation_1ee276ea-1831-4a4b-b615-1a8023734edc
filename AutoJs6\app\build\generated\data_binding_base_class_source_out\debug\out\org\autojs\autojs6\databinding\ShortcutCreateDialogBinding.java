// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorCheckBox;
import org.autojs.autojs6.R;

public final class ShortcutCreateDialogBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView icon;

  @NonNull
  public final AppCompatEditText name;

  @NonNull
  public final ThemeColorCheckBox useAndroidNShortcut;

  private ShortcutCreateDialogBinding(@NonNull LinearLayout rootView, @NonNull ImageView icon,
      @NonNull AppCompatEditText name, @NonNull ThemeColorCheckBox useAndroidNShortcut) {
    this.rootView = rootView;
    this.icon = icon;
    this.name = name;
    this.useAndroidNShortcut = useAndroidNShortcut;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ShortcutCreateDialogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ShortcutCreateDialogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.shortcut_create_dialog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ShortcutCreateDialogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.icon;
      ImageView icon = ViewBindings.findChildViewById(rootView, id);
      if (icon == null) {
        break missingId;
      }

      id = R.id.name;
      AppCompatEditText name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.use_android_n_shortcut;
      ThemeColorCheckBox useAndroidNShortcut = ViewBindings.findChildViewById(rootView, id);
      if (useAndroidNShortcut == null) {
        break missingId;
      }

      return new ShortcutCreateDialogBinding((LinearLayout) rootView, icon, name,
          useAndroidNShortcut);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
