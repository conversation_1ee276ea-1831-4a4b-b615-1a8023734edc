// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class DialogScriptLoopBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatEditText loopDelay;

  @NonNull
  public final AppCompatEditText loopInterval;

  @NonNull
  public final AppCompatEditText loopTimes;

  private DialogScriptLoopBinding(@NonNull LinearLayout rootView,
      @NonNull AppCompatEditText loopDelay, @NonNull AppCompatEditText loopInterval,
      @NonNull AppCompatEditText loopTimes) {
    this.rootView = rootView;
    this.loopDelay = loopDelay;
    this.loopInterval = loopInterval;
    this.loopTimes = loopTimes;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogScriptLoopBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogScriptLoopBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_script_loop, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogScriptLoopBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.loop_delay;
      AppCompatEditText loopDelay = ViewBindings.findChildViewById(rootView, id);
      if (loopDelay == null) {
        break missingId;
      }

      id = R.id.loop_interval;
      AppCompatEditText loopInterval = ViewBindings.findChildViewById(rootView, id);
      if (loopInterval == null) {
        break missingId;
      }

      id = R.id.loop_times;
      AppCompatEditText loopTimes = ViewBindings.findChildViewById(rootView, id);
      if (loopTimes == null) {
        break missingId;
      }

      return new DialogScriptLoopBinding((LinearLayout) rootView, loopDelay, loopInterval,
          loopTimes);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
