// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorSwipeRefreshLayout;
import org.autojs.autojs.ui.widget.NestedWebView;
import org.autojs.autojs6.R;

public final class EwebviewBinding implements ViewBinding {
  @NonNull
  private final View rootView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ThemeColorSwipeRefreshLayout swipeRefreshLayout;

  @NonNull
  public final NestedWebView webView;

  private EwebviewBinding(@NonNull View rootView, @NonNull ProgressBar progressBar,
      @NonNull ThemeColorSwipeRefreshLayout swipeRefreshLayout, @NonNull NestedWebView webView) {
    this.rootView = rootView;
    this.progressBar = progressBar;
    this.swipeRefreshLayout = swipeRefreshLayout;
    this.webView = webView;
  }

  @Override
  @NonNull
  public View getRoot() {
    return rootView;
  }

  @NonNull
  public static EwebviewBinding inflate(@NonNull LayoutInflater inflater,
      @NonNull ViewGroup parent) {
    if (parent == null) {
      throw new NullPointerException("parent");
    }
    inflater.inflate(R.layout.ewebview, parent);
    return bind(parent);
  }

  @NonNull
  public static EwebviewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.swipe_refresh_layout;
      ThemeColorSwipeRefreshLayout swipeRefreshLayout = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefreshLayout == null) {
        break missingId;
      }

      id = R.id.web_view;
      NestedWebView webView = ViewBindings.findChildViewById(rootView, id);
      if (webView == null) {
        break missingId;
      }

      return new EwebviewBinding(rootView, progressBar, swipeRefreshLayout, webView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
