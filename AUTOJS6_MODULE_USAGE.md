# AutoJs6 Module 使用指南

## 概述

我们成功创建了一个独立的AutoJs6模块，它提供了简化的JavaScript脚本执行功能。这个模块基于Rhino JavaScript引擎，提供了一个清晰的API接口。

## 架构优势

### 1. **模块化设计**
- 独立的module，便于维护和复用
- 清晰的API边界
- 避免了复杂的依赖冲突

### 2. **简化的集成**
- 只需要添加一行依赖：`implementation project(':autojs6-module')`
- 无需复杂的配置
- 基于标准的Android Library架构

### 3. **轻量级实现**
- 基于Rhino JavaScript引擎
- 最小化的依赖
- 高性能的脚本执行

## 使用方法

### 1. 初始化模块

```java
// 在Application或Activity中初始化
AutoJs6Module autoJs6Module = AutoJs6Module.getInstance();
autoJs6Module.initialize(context);
```

### 2. 创建和执行脚本

```java
// 创建脚本
Script script = new Script("测试脚本", "console.log('Hello AutoJs6!');");

// 同步执行
ScriptResult result = autoJs6Module.executeScript(script);
if (result.isSuccess()) {
    Log.d("Script", "执行成功: " + result.getResult());
} else {
    Log.e("Script", "执行失败: " + result.getError());
}

// 异步执行
autoJs6Module.executeScriptAsync(script)
    .thenAccept(result -> {
        if (result.isSuccess()) {
            // 处理成功结果
        } else {
            // 处理错误
        }
    });
```

### 3. 脚本管理

```java
// 停止所有脚本
autoJs6Module.stopAllScripts();

// 获取运行中的脚本数量
int count = autoJs6Module.getRunningScriptCount();

// 检查初始化状态
boolean initialized = autoJs6Module.isInitialized();
```

## 支持的JavaScript API

当前模块提供了以下JavaScript API：

### 1. Console对象
```javascript
console.log("信息日志");
console.error("错误日志");
console.warn("警告日志");
```

### 2. Utils工具函数
```javascript
utils.log("日志信息");
utils.sleep(1000); // 休眠1秒
utils.toast("显示Toast"); // 显示提示信息
```

### 3. Android上下文访问
```javascript
// 可以访问Android Context（简化版）
// androidContext 变量可用于访问Android功能
```

## 项目结构

```
autojs6-module/
├── src/main/java/com/bm/autojs6/module/
│   ├── AutoJs6Module.java          # 主要API接口
│   ├── engine/
│   │   └── ScriptEngine.java       # 脚本执行引擎
│   └── model/
│       ├── Script.java             # 脚本模型
│       └── ScriptResult.java       # 执行结果模型
├── build.gradle                    # 模块构建配置
└── src/main/AndroidManifest.xml    # 模块清单文件
```

## 扩展功能

### 1. 添加新的JavaScript API

在`ScriptEngine.injectAndroidAPIs()`方法中添加新的API：

```java
// 添加新的API对象
ScriptableObject.putProperty(scope, "myAPI", new MyAPIObject());
```

### 2. 集成更多AutoJs6功能

可以逐步从原始AutoJs6项目中集成更多功能：
- 无障碍服务支持
- 图像识别功能
- 网络请求API
- 文件操作API

### 3. 性能优化

- 脚本缓存机制
- 预编译脚本
- 多线程执行优化

## 下一步计划

1. **功能扩展**：逐步集成AutoJs6的核心功能
2. **API完善**：添加更多JavaScript API
3. **性能优化**：提升脚本执行性能
4. **文档完善**：提供更详细的API文档

## 总结

通过创建独立的AutoJs6模块，我们实现了：
- ✅ 清晰的模块化架构
- ✅ 简化的集成方式
- ✅ 基本的JavaScript脚本执行功能
- ✅ 可扩展的API设计
- ✅ 避免了复杂的依赖冲突

这个方案为将来集成更多AutoJs6功能提供了良好的基础。
