// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.widget.LevelBeamView;
import org.autojs.autojs6.R;

public final class LayoutHierarchyViewItemBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView dataItemArrow;

  @NonNull
  public final TextView dataItemInfo;

  @NonNull
  public final LevelBeamView dataItemLevelBeam;

  @NonNull
  public final TextView dataItemName;

  private LayoutHierarchyViewItemBinding(@NonNull RelativeLayout rootView,
      @NonNull ImageView dataItemArrow, @NonNull TextView dataItemInfo,
      @NonNull LevelBeamView dataItemLevelBeam, @NonNull TextView dataItemName) {
    this.rootView = rootView;
    this.dataItemArrow = dataItemArrow;
    this.dataItemInfo = dataItemInfo;
    this.dataItemLevelBeam = dataItemLevelBeam;
    this.dataItemName = dataItemName;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutHierarchyViewItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutHierarchyViewItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_hierarchy_view_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutHierarchyViewItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.dataItemArrow;
      ImageView dataItemArrow = ViewBindings.findChildViewById(rootView, id);
      if (dataItemArrow == null) {
        break missingId;
      }

      id = R.id.dataItemInfo;
      TextView dataItemInfo = ViewBindings.findChildViewById(rootView, id);
      if (dataItemInfo == null) {
        break missingId;
      }

      id = R.id.dataItemLevelBeam;
      LevelBeamView dataItemLevelBeam = ViewBindings.findChildViewById(rootView, id);
      if (dataItemLevelBeam == null) {
        break missingId;
      }

      id = R.id.dataItemName;
      TextView dataItemName = ViewBindings.findChildViewById(rootView, id);
      if (dataItemName == null) {
        break missingId;
      }

      return new LayoutHierarchyViewItemBinding((RelativeLayout) rootView, dataItemArrow,
          dataItemInfo, dataItemLevelBeam, dataItemName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
