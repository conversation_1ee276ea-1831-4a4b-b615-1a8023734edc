// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ExplorerFileBinding implements ViewBinding {
  @NonNull
  private final LinearLayoutCompat rootView;

  @NonNull
  public final LinearLayout actionIconContainer;

  @NonNull
  public final ImageView edit;

  @NonNull
  public final ImageView info;

  @NonNull
  public final ImageView install;

  @NonNull
  public final LinearLayout item;

  @NonNull
  public final ImageView more;

  @NonNull
  public final TextView name;

  @NonNull
  public final ImageView run;

  @NonNull
  public final TextView scriptFileDate;

  @NonNull
  public final TextView scriptFileSize;

  private ExplorerFileBinding(@NonNull LinearLayoutCompat rootView,
      @NonNull LinearLayout actionIconContainer, @NonNull ImageView edit, @NonNull ImageView info,
      @NonNull ImageView install, @NonNull LinearLayout item, @NonNull ImageView more,
      @NonNull TextView name, @NonNull ImageView run, @NonNull TextView scriptFileDate,
      @NonNull TextView scriptFileSize) {
    this.rootView = rootView;
    this.actionIconContainer = actionIconContainer;
    this.edit = edit;
    this.info = info;
    this.install = install;
    this.item = item;
    this.more = more;
    this.name = name;
    this.run = run;
    this.scriptFileDate = scriptFileDate;
    this.scriptFileSize = scriptFileSize;
  }

  @Override
  @NonNull
  public LinearLayoutCompat getRoot() {
    return rootView;
  }

  @NonNull
  public static ExplorerFileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExplorerFileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.explorer_file, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExplorerFileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.action_icon_container;
      LinearLayout actionIconContainer = ViewBindings.findChildViewById(rootView, id);
      if (actionIconContainer == null) {
        break missingId;
      }

      id = R.id.edit;
      ImageView edit = ViewBindings.findChildViewById(rootView, id);
      if (edit == null) {
        break missingId;
      }

      id = R.id.info;
      ImageView info = ViewBindings.findChildViewById(rootView, id);
      if (info == null) {
        break missingId;
      }

      id = R.id.install;
      ImageView install = ViewBindings.findChildViewById(rootView, id);
      if (install == null) {
        break missingId;
      }

      id = R.id.item;
      LinearLayout item = ViewBindings.findChildViewById(rootView, id);
      if (item == null) {
        break missingId;
      }

      id = R.id.more;
      ImageView more = ViewBindings.findChildViewById(rootView, id);
      if (more == null) {
        break missingId;
      }

      id = R.id.name;
      TextView name = ViewBindings.findChildViewById(rootView, id);
      if (name == null) {
        break missingId;
      }

      id = R.id.run;
      ImageView run = ViewBindings.findChildViewById(rootView, id);
      if (run == null) {
        break missingId;
      }

      id = R.id.script_file_date;
      TextView scriptFileDate = ViewBindings.findChildViewById(rootView, id);
      if (scriptFileDate == null) {
        break missingId;
      }

      id = R.id.script_file_size;
      TextView scriptFileSize = ViewBindings.findChildViewById(rootView, id);
      if (scriptFileSize == null) {
        break missingId;
      }

      return new ExplorerFileBinding((LinearLayoutCompat) rootView, actionIconContainer, edit, info,
          install, item, more, name, run, scriptFileDate, scriptFileSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
