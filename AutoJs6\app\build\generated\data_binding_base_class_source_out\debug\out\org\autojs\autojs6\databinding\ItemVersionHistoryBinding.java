// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ItemVersionHistoryBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView chevron;

  @NonNull
  public final LinearLayout summaryContainer;

  @NonNull
  public final LinearLayout titleContainer;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvDependencyCount;

  @NonNull
  public final LinearLayout tvDependencyCountContainer;

  @NonNull
  public final TextView tvFeatureCount;

  @NonNull
  public final LinearLayout tvFeatureCountContainer;

  @NonNull
  public final TextView tvFixCount;

  @NonNull
  public final LinearLayout tvFixCountContainer;

  @NonNull
  public final TextView tvHintCount;

  @NonNull
  public final LinearLayout tvHintCountContainer;

  @NonNull
  public final TextView tvImprovementCount;

  @NonNull
  public final LinearLayout tvImprovementCountContainer;

  @NonNull
  public final TextView tvLines;

  @NonNull
  public final TextView tvTitle;

  private ItemVersionHistoryBinding(@NonNull CardView rootView, @NonNull ImageView chevron,
      @NonNull LinearLayout summaryContainer, @NonNull LinearLayout titleContainer,
      @NonNull TextView tvDate, @NonNull TextView tvDependencyCount,
      @NonNull LinearLayout tvDependencyCountContainer, @NonNull TextView tvFeatureCount,
      @NonNull LinearLayout tvFeatureCountContainer, @NonNull TextView tvFixCount,
      @NonNull LinearLayout tvFixCountContainer, @NonNull TextView tvHintCount,
      @NonNull LinearLayout tvHintCountContainer, @NonNull TextView tvImprovementCount,
      @NonNull LinearLayout tvImprovementCountContainer, @NonNull TextView tvLines,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.chevron = chevron;
    this.summaryContainer = summaryContainer;
    this.titleContainer = titleContainer;
    this.tvDate = tvDate;
    this.tvDependencyCount = tvDependencyCount;
    this.tvDependencyCountContainer = tvDependencyCountContainer;
    this.tvFeatureCount = tvFeatureCount;
    this.tvFeatureCountContainer = tvFeatureCountContainer;
    this.tvFixCount = tvFixCount;
    this.tvFixCountContainer = tvFixCountContainer;
    this.tvHintCount = tvHintCount;
    this.tvHintCountContainer = tvHintCountContainer;
    this.tvImprovementCount = tvImprovementCount;
    this.tvImprovementCountContainer = tvImprovementCountContainer;
    this.tvLines = tvLines;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemVersionHistoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemVersionHistoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_version_history, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemVersionHistoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chevron;
      ImageView chevron = ViewBindings.findChildViewById(rootView, id);
      if (chevron == null) {
        break missingId;
      }

      id = R.id.summary_container;
      LinearLayout summaryContainer = ViewBindings.findChildViewById(rootView, id);
      if (summaryContainer == null) {
        break missingId;
      }

      id = R.id.title_container;
      LinearLayout titleContainer = ViewBindings.findChildViewById(rootView, id);
      if (titleContainer == null) {
        break missingId;
      }

      id = R.id.tvDate;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tvDependencyCount;
      TextView tvDependencyCount = ViewBindings.findChildViewById(rootView, id);
      if (tvDependencyCount == null) {
        break missingId;
      }

      id = R.id.tvDependencyCountContainer;
      LinearLayout tvDependencyCountContainer = ViewBindings.findChildViewById(rootView, id);
      if (tvDependencyCountContainer == null) {
        break missingId;
      }

      id = R.id.tvFeatureCount;
      TextView tvFeatureCount = ViewBindings.findChildViewById(rootView, id);
      if (tvFeatureCount == null) {
        break missingId;
      }

      id = R.id.tvFeatureCountContainer;
      LinearLayout tvFeatureCountContainer = ViewBindings.findChildViewById(rootView, id);
      if (tvFeatureCountContainer == null) {
        break missingId;
      }

      id = R.id.tvFixCount;
      TextView tvFixCount = ViewBindings.findChildViewById(rootView, id);
      if (tvFixCount == null) {
        break missingId;
      }

      id = R.id.tvFixCountContainer;
      LinearLayout tvFixCountContainer = ViewBindings.findChildViewById(rootView, id);
      if (tvFixCountContainer == null) {
        break missingId;
      }

      id = R.id.tvHintCount;
      TextView tvHintCount = ViewBindings.findChildViewById(rootView, id);
      if (tvHintCount == null) {
        break missingId;
      }

      id = R.id.tvHintCountContainer;
      LinearLayout tvHintCountContainer = ViewBindings.findChildViewById(rootView, id);
      if (tvHintCountContainer == null) {
        break missingId;
      }

      id = R.id.tvImprovementCount;
      TextView tvImprovementCount = ViewBindings.findChildViewById(rootView, id);
      if (tvImprovementCount == null) {
        break missingId;
      }

      id = R.id.tvImprovementCountContainer;
      LinearLayout tvImprovementCountContainer = ViewBindings.findChildViewById(rootView, id);
      if (tvImprovementCountContainer == null) {
        break missingId;
      }

      id = R.id.tvLines;
      TextView tvLines = ViewBindings.findChildViewById(rootView, id);
      if (tvLines == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ItemVersionHistoryBinding((CardView) rootView, chevron, summaryContainer,
          titleContainer, tvDate, tvDependencyCount, tvDependencyCountContainer, tvFeatureCount,
          tvFeatureCountContainer, tvFixCount, tvFixCountContainer, tvHintCount,
          tvHintCountContainer, tvImprovementCount, tvImprovementCountContainer, tvLines, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
