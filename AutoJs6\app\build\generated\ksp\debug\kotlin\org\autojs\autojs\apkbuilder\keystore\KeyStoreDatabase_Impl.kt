package org.autojs.autojs.apkbuilder.keystore

import androidx.room.InvalidationTracker
import androidx.room.RoomOpenDelegate
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.room.util.TableInfo
import androidx.room.util.TableInfo.Companion.read
import androidx.room.util.dropFtsSyncTriggers
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Lazy
import kotlin.String
import kotlin.Suppress
import kotlin.collections.List
import kotlin.collections.Map
import kotlin.collections.MutableList
import kotlin.collections.MutableMap
import kotlin.collections.MutableSet
import kotlin.collections.Set
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.mutableSetOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class KeyStoreDatabase_Impl : KeyStoreDatabase() {
  private val _keyStoreDao: Lazy<KeyStoreDao> = lazy {
    KeyStoreDao_Impl(this)
  }

  protected override fun createOpenDelegate(): RoomOpenDelegate {
    val _openDelegate: RoomOpenDelegate = object : RoomOpenDelegate(1,
        "46ed6c4fe132a89ef9fc828d80df3715", "ff919c44a50ca18c5779779fb7d95665") {
      public override fun createAllTables(connection: SQLiteConnection) {
        connection.execSQL("CREATE TABLE IF NOT EXISTS `KeyStore` (`absolutePath` TEXT NOT NULL, `filename` TEXT NOT NULL, `password` TEXT NOT NULL, `alias` TEXT NOT NULL, `alias_password` TEXT NOT NULL, `verified` INTEGER NOT NULL, PRIMARY KEY(`absolutePath`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)")
        connection.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '46ed6c4fe132a89ef9fc828d80df3715')")
      }

      public override fun dropAllTables(connection: SQLiteConnection) {
        connection.execSQL("DROP TABLE IF EXISTS `KeyStore`")
      }

      public override fun onCreate(connection: SQLiteConnection) {
      }

      public override fun onOpen(connection: SQLiteConnection) {
        internalInitInvalidationTracker(connection)
      }

      public override fun onPreMigrate(connection: SQLiteConnection) {
        dropFtsSyncTriggers(connection)
      }

      public override fun onPostMigrate(connection: SQLiteConnection) {
      }

      public override fun onValidateSchema(connection: SQLiteConnection):
          RoomOpenDelegate.ValidationResult {
        val _columnsKeyStore: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsKeyStore.put("absolutePath", TableInfo.Column("absolutePath", "TEXT", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsKeyStore.put("filename", TableInfo.Column("filename", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsKeyStore.put("password", TableInfo.Column("password", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsKeyStore.put("alias", TableInfo.Column("alias", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsKeyStore.put("alias_password", TableInfo.Column("alias_password", "TEXT", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsKeyStore.put("verified", TableInfo.Column("verified", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysKeyStore: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesKeyStore: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoKeyStore: TableInfo = TableInfo("KeyStore", _columnsKeyStore, _foreignKeysKeyStore,
            _indicesKeyStore)
        val _existingKeyStore: TableInfo = read(connection, "KeyStore")
        if (!_infoKeyStore.equals(_existingKeyStore)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |KeyStore(org.autojs.autojs.apkbuilder.keystore.KeyStore).
              | Expected:
              |""".trimMargin() + _infoKeyStore + """
              |
              | Found:
              |""".trimMargin() + _existingKeyStore)
        }
        return RoomOpenDelegate.ValidationResult(true, null)
      }
    }
    return _openDelegate
  }

  protected override fun createInvalidationTracker(): InvalidationTracker {
    val _shadowTablesMap: MutableMap<String, String> = mutableMapOf()
    val _viewTables: MutableMap<String, Set<String>> = mutableMapOf()
    return InvalidationTracker(this, _shadowTablesMap, _viewTables, "KeyStore")
  }

  public override fun clearAllTables() {
    super.performClear(false, "KeyStore")
  }

  protected override fun getRequiredTypeConverterClasses(): Map<KClass<*>, List<KClass<*>>> {
    val _typeConvertersMap: MutableMap<KClass<*>, List<KClass<*>>> = mutableMapOf()
    _typeConvertersMap.put(KeyStoreDao::class, KeyStoreDao_Impl.getRequiredConverters())
    return _typeConvertersMap
  }

  public override fun getRequiredAutoMigrationSpecClasses(): Set<KClass<out AutoMigrationSpec>> {
    val _autoMigrationSpecsSet: MutableSet<KClass<out AutoMigrationSpec>> = mutableSetOf()
    return _autoMigrationSpecsSet
  }

  public override
      fun createAutoMigrations(autoMigrationSpecs: Map<KClass<out AutoMigrationSpec>, AutoMigrationSpec>):
      List<Migration> {
    val _autoMigrations: MutableList<Migration> = mutableListOf()
    return _autoMigrations
  }

  public override fun keyStoreDao(): KeyStoreDao = _keyStoreDao.value
}
