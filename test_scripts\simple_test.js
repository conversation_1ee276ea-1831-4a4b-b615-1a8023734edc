// 简单的JavaScript测试脚本
console.log("Hello AutoJs6 Module!");

// 测试utils工具函数
utils.log("这是通过utils.log输出的消息");

// 测试变量和计算
var a = 10;
var b = 20;
var result = a + b;
console.log("计算结果: " + a + " + " + b + " = " + result);

// 测试循环
for (var i = 1; i <= 3; i++) {
    console.log("循环第 " + i + " 次");
}

// 测试函数定义
function greet(name) {
    return "Hello, " + name + "!";
}

var greeting = greet("AutoJs6");
console.log(greeting);

// 测试对象
var person = {
    name: "张三",
    age: 25,
    sayHello: function() {
        return "我是" + this.name + "，今年" + this.age + "岁";
    }
};

console.log(person.sayHello());

// 测试数组
var numbers = [1, 2, 3, 4, 5];
var sum = 0;
for (var i = 0; i < numbers.length; i++) {
    sum += numbers[i];
}
console.log("数组求和: " + sum);

// 测试字符串操作
var text = "AutoJs6 Module Test";
console.log("原文本: " + text);
console.log("转大写: " + text.toUpperCase());
console.log("转小写: " + text.toLowerCase());
console.log("文本长度: " + text.length);

// 测试日期
var now = new Date();
console.log("当前时间: " + now.toString());

// 最终输出
console.log("脚本执行完成！");
"脚本执行成功 - 所有测试通过";
