// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ThemeColorRecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import io.codetail.widget.RevealFrameLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.widget.AdaptiveTitleToolbar;
import org.autojs.autojs6.R;

public final class MtActivityColorLibrariesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppBarLayout appBar;

  @NonNull
  public final RevealFrameLayout appBarContainer;

  @NonNull
  public final ThemeColorRecyclerView colorLibrariesRecyclerView;

  @NonNull
  public final AdaptiveTitleToolbar toolbar;

  private MtActivityColorLibrariesBinding(@NonNull LinearLayout rootView,
      @NonNull AppBarLayout appBar, @NonNull RevealFrameLayout appBarContainer,
      @NonNull ThemeColorRecyclerView colorLibrariesRecyclerView,
      @NonNull AdaptiveTitleToolbar toolbar) {
    this.rootView = rootView;
    this.appBar = appBar;
    this.appBarContainer = appBarContainer;
    this.colorLibrariesRecyclerView = colorLibrariesRecyclerView;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MtActivityColorLibrariesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MtActivityColorLibrariesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.mt_activity_color_libraries, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MtActivityColorLibrariesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar;
      AppBarLayout appBar = ViewBindings.findChildViewById(rootView, id);
      if (appBar == null) {
        break missingId;
      }

      id = R.id.appBarContainer;
      RevealFrameLayout appBarContainer = ViewBindings.findChildViewById(rootView, id);
      if (appBarContainer == null) {
        break missingId;
      }

      id = R.id.color_libraries_recycler_view;
      ThemeColorRecyclerView colorLibrariesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (colorLibrariesRecyclerView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      AdaptiveTitleToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new MtActivityColorLibrariesBinding((LinearLayout) rootView, appBar, appBarContainer,
          colorLibrariesRecyclerView, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
