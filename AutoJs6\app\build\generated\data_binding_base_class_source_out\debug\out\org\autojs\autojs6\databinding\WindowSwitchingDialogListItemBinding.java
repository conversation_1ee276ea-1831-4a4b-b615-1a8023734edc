// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class WindowSwitchingDialogListItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView orderValue;

  @NonNull
  public final TextView packageColon;

  @NonNull
  public final TextView packageLabel;

  @NonNull
  public final ConstraintLayout packageParent;

  @NonNull
  public final TextView packageValue;

  @NonNull
  public final TextView rootNodeColon;

  @NonNull
  public final TextView rootNodeLabel;

  @NonNull
  public final ConstraintLayout rootNodeParent;

  @NonNull
  public final TextView rootNodeValue;

  @NonNull
  public final TextView titleColon;

  @NonNull
  public final TextView titleLabel;

  @NonNull
  public final ConstraintLayout titleParent;

  @NonNull
  public final TextView titleValue;

  @NonNull
  public final TextView typeColon;

  @NonNull
  public final TextView typeLabel;

  @NonNull
  public final ConstraintLayout typeParent;

  @NonNull
  public final TextView typeValue;

  private WindowSwitchingDialogListItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView orderValue, @NonNull TextView packageColon, @NonNull TextView packageLabel,
      @NonNull ConstraintLayout packageParent, @NonNull TextView packageValue,
      @NonNull TextView rootNodeColon, @NonNull TextView rootNodeLabel,
      @NonNull ConstraintLayout rootNodeParent, @NonNull TextView rootNodeValue,
      @NonNull TextView titleColon, @NonNull TextView titleLabel,
      @NonNull ConstraintLayout titleParent, @NonNull TextView titleValue,
      @NonNull TextView typeColon, @NonNull TextView typeLabel,
      @NonNull ConstraintLayout typeParent, @NonNull TextView typeValue) {
    this.rootView = rootView;
    this.orderValue = orderValue;
    this.packageColon = packageColon;
    this.packageLabel = packageLabel;
    this.packageParent = packageParent;
    this.packageValue = packageValue;
    this.rootNodeColon = rootNodeColon;
    this.rootNodeLabel = rootNodeLabel;
    this.rootNodeParent = rootNodeParent;
    this.rootNodeValue = rootNodeValue;
    this.titleColon = titleColon;
    this.titleLabel = titleLabel;
    this.titleParent = titleParent;
    this.titleValue = titleValue;
    this.typeColon = typeColon;
    this.typeLabel = typeLabel;
    this.typeParent = typeParent;
    this.typeValue = typeValue;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static WindowSwitchingDialogListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static WindowSwitchingDialogListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.window_switching_dialog_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static WindowSwitchingDialogListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.order_value;
      TextView orderValue = ViewBindings.findChildViewById(rootView, id);
      if (orderValue == null) {
        break missingId;
      }

      id = R.id.package_colon;
      TextView packageColon = ViewBindings.findChildViewById(rootView, id);
      if (packageColon == null) {
        break missingId;
      }

      id = R.id.package_label;
      TextView packageLabel = ViewBindings.findChildViewById(rootView, id);
      if (packageLabel == null) {
        break missingId;
      }

      id = R.id.package_parent;
      ConstraintLayout packageParent = ViewBindings.findChildViewById(rootView, id);
      if (packageParent == null) {
        break missingId;
      }

      id = R.id.package_value;
      TextView packageValue = ViewBindings.findChildViewById(rootView, id);
      if (packageValue == null) {
        break missingId;
      }

      id = R.id.root_node_colon;
      TextView rootNodeColon = ViewBindings.findChildViewById(rootView, id);
      if (rootNodeColon == null) {
        break missingId;
      }

      id = R.id.root_node_label;
      TextView rootNodeLabel = ViewBindings.findChildViewById(rootView, id);
      if (rootNodeLabel == null) {
        break missingId;
      }

      id = R.id.root_node_parent;
      ConstraintLayout rootNodeParent = ViewBindings.findChildViewById(rootView, id);
      if (rootNodeParent == null) {
        break missingId;
      }

      id = R.id.root_node_value;
      TextView rootNodeValue = ViewBindings.findChildViewById(rootView, id);
      if (rootNodeValue == null) {
        break missingId;
      }

      id = R.id.title_colon;
      TextView titleColon = ViewBindings.findChildViewById(rootView, id);
      if (titleColon == null) {
        break missingId;
      }

      id = R.id.title_label;
      TextView titleLabel = ViewBindings.findChildViewById(rootView, id);
      if (titleLabel == null) {
        break missingId;
      }

      id = R.id.title_parent;
      ConstraintLayout titleParent = ViewBindings.findChildViewById(rootView, id);
      if (titleParent == null) {
        break missingId;
      }

      id = R.id.title_value;
      TextView titleValue = ViewBindings.findChildViewById(rootView, id);
      if (titleValue == null) {
        break missingId;
      }

      id = R.id.type_colon;
      TextView typeColon = ViewBindings.findChildViewById(rootView, id);
      if (typeColon == null) {
        break missingId;
      }

      id = R.id.type_label;
      TextView typeLabel = ViewBindings.findChildViewById(rootView, id);
      if (typeLabel == null) {
        break missingId;
      }

      id = R.id.type_parent;
      ConstraintLayout typeParent = ViewBindings.findChildViewById(rootView, id);
      if (typeParent == null) {
        break missingId;
      }

      id = R.id.type_value;
      TextView typeValue = ViewBindings.findChildViewById(rootView, id);
      if (typeValue == null) {
        break missingId;
      }

      return new WindowSwitchingDialogListItemBinding((ConstraintLayout) rootView, orderValue,
          packageColon, packageLabel, packageParent, packageValue, rootNodeColon, rootNodeLabel,
          rootNodeParent, rootNodeValue, titleColon, titleLabel, titleParent, titleValue, typeColon,
          typeLabel, typeParent, typeValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
