// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.ui.edit.EditorView;
import org.autojs.autojs6.R;

public final class ActivityTaskerScriptEditBinding implements ViewBinding {
  @NonNull
  private final EditorView rootView;

  @NonNull
  public final EditorView editorView;

  private ActivityTaskerScriptEditBinding(@NonNull EditorView rootView,
      @NonNull EditorView editorView) {
    this.rootView = rootView;
    this.editorView = editorView;
  }

  @Override
  @NonNull
  public EditorView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTaskerScriptEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTaskerScriptEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_tasker_script_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTaskerScriptEditBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    EditorView editorView = (EditorView) rootView;

    return new ActivityTaskerScriptEditBinding((EditorView) rootView, editorView);
  }
}
