// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.theme.widget.ThemeColorSeekBar;
import org.autojs.autojs6.R;

public final class DialogTextSizeSettingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView previewText;

  @NonNull
  public final ThemeColorSeekBar seekbar;

  private DialogTextSizeSettingBinding(@NonNull LinearLayout rootView,
      @NonNull TextView previewText, @NonNull ThemeColorSeekBar seekbar) {
    this.rootView = rootView;
    this.previewText = previewText;
    this.seekbar = seekbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTextSizeSettingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTextSizeSettingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_text_size_setting, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTextSizeSettingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.preview_text;
      TextView previewText = ViewBindings.findChildViewById(rootView, id);
      if (previewText == null) {
        break missingId;
      }

      id = R.id.seekbar;
      ThemeColorSeekBar seekbar = ViewBindings.findChildViewById(rootView, id);
      if (seekbar == null) {
        break missingId;
      }

      return new DialogTextSizeSettingBinding((LinearLayout) rootView, previewText, seekbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
