// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Guideline;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ApkFileInfoDialogListItemBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView deviceSdkColon;

  @NonNull
  public final Guideline deviceSdkGuideline;

  @NonNull
  public final TextView deviceSdkLabel;

  @NonNull
  public final ConstraintLayout deviceSdkParent;

  @NonNull
  public final TextView deviceSdkValue;

  @NonNull
  public final TextView fileSizeColon;

  @NonNull
  public final Guideline fileSizeGuideline;

  @NonNull
  public final TextView fileSizeLabel;

  @NonNull
  public final ConstraintLayout fileSizeParent;

  @NonNull
  public final TextView fileSizeValue;

  @NonNull
  public final TextView installedVersionColon;

  @NonNull
  public final Guideline installedVersionGuideline;

  @NonNull
  public final TextView installedVersionLabel;

  @NonNull
  public final ConstraintLayout installedVersionParent;

  @NonNull
  public final TextView installedVersionValue;

  @NonNull
  public final TextView labelNameColon;

  @NonNull
  public final Guideline labelNameGuideline;

  @NonNull
  public final TextView labelNameLabel;

  @NonNull
  public final ConstraintLayout labelNameParent;

  @NonNull
  public final TextView labelNameValue;

  @NonNull
  public final TextView minSdkColon;

  @NonNull
  public final Guideline minSdkGuideline;

  @NonNull
  public final TextView minSdkLabel;

  @NonNull
  public final ConstraintLayout minSdkParent;

  @NonNull
  public final TextView minSdkValue;

  @NonNull
  public final TextView packageNameColon;

  @NonNull
  public final Guideline packageNameGuideline;

  @NonNull
  public final TextView packageNameLabel;

  @NonNull
  public final ConstraintLayout packageNameParent;

  @NonNull
  public final TextView packageNameValue;

  @NonNull
  public final TextView signatureSchemeColon;

  @NonNull
  public final Guideline signatureSchemeGuideline;

  @NonNull
  public final TextView signatureSchemeLabel;

  @NonNull
  public final ConstraintLayout signatureSchemeParent;

  @NonNull
  public final TextView signatureSchemeValue;

  @NonNull
  public final TextView targetSdkColon;

  @NonNull
  public final Guideline targetSdkGuideline;

  @NonNull
  public final TextView targetSdkLabel;

  @NonNull
  public final ConstraintLayout targetSdkParent;

  @NonNull
  public final TextView targetSdkValue;

  @NonNull
  public final TextView versionPlaceholderColon;

  @NonNull
  public final Guideline versionPlaceholderGuideline;

  @NonNull
  public final TextView versionPlaceholderLabel;

  @NonNull
  public final ConstraintLayout versionPlaceholderParent;

  @NonNull
  public final TextView versionPlaceholderValue;

  private ApkFileInfoDialogListItemBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView deviceSdkColon, @NonNull Guideline deviceSdkGuideline,
      @NonNull TextView deviceSdkLabel, @NonNull ConstraintLayout deviceSdkParent,
      @NonNull TextView deviceSdkValue, @NonNull TextView fileSizeColon,
      @NonNull Guideline fileSizeGuideline, @NonNull TextView fileSizeLabel,
      @NonNull ConstraintLayout fileSizeParent, @NonNull TextView fileSizeValue,
      @NonNull TextView installedVersionColon, @NonNull Guideline installedVersionGuideline,
      @NonNull TextView installedVersionLabel, @NonNull ConstraintLayout installedVersionParent,
      @NonNull TextView installedVersionValue, @NonNull TextView labelNameColon,
      @NonNull Guideline labelNameGuideline, @NonNull TextView labelNameLabel,
      @NonNull ConstraintLayout labelNameParent, @NonNull TextView labelNameValue,
      @NonNull TextView minSdkColon, @NonNull Guideline minSdkGuideline,
      @NonNull TextView minSdkLabel, @NonNull ConstraintLayout minSdkParent,
      @NonNull TextView minSdkValue, @NonNull TextView packageNameColon,
      @NonNull Guideline packageNameGuideline, @NonNull TextView packageNameLabel,
      @NonNull ConstraintLayout packageNameParent, @NonNull TextView packageNameValue,
      @NonNull TextView signatureSchemeColon, @NonNull Guideline signatureSchemeGuideline,
      @NonNull TextView signatureSchemeLabel, @NonNull ConstraintLayout signatureSchemeParent,
      @NonNull TextView signatureSchemeValue, @NonNull TextView targetSdkColon,
      @NonNull Guideline targetSdkGuideline, @NonNull TextView targetSdkLabel,
      @NonNull ConstraintLayout targetSdkParent, @NonNull TextView targetSdkValue,
      @NonNull TextView versionPlaceholderColon, @NonNull Guideline versionPlaceholderGuideline,
      @NonNull TextView versionPlaceholderLabel, @NonNull ConstraintLayout versionPlaceholderParent,
      @NonNull TextView versionPlaceholderValue) {
    this.rootView = rootView;
    this.deviceSdkColon = deviceSdkColon;
    this.deviceSdkGuideline = deviceSdkGuideline;
    this.deviceSdkLabel = deviceSdkLabel;
    this.deviceSdkParent = deviceSdkParent;
    this.deviceSdkValue = deviceSdkValue;
    this.fileSizeColon = fileSizeColon;
    this.fileSizeGuideline = fileSizeGuideline;
    this.fileSizeLabel = fileSizeLabel;
    this.fileSizeParent = fileSizeParent;
    this.fileSizeValue = fileSizeValue;
    this.installedVersionColon = installedVersionColon;
    this.installedVersionGuideline = installedVersionGuideline;
    this.installedVersionLabel = installedVersionLabel;
    this.installedVersionParent = installedVersionParent;
    this.installedVersionValue = installedVersionValue;
    this.labelNameColon = labelNameColon;
    this.labelNameGuideline = labelNameGuideline;
    this.labelNameLabel = labelNameLabel;
    this.labelNameParent = labelNameParent;
    this.labelNameValue = labelNameValue;
    this.minSdkColon = minSdkColon;
    this.minSdkGuideline = minSdkGuideline;
    this.minSdkLabel = minSdkLabel;
    this.minSdkParent = minSdkParent;
    this.minSdkValue = minSdkValue;
    this.packageNameColon = packageNameColon;
    this.packageNameGuideline = packageNameGuideline;
    this.packageNameLabel = packageNameLabel;
    this.packageNameParent = packageNameParent;
    this.packageNameValue = packageNameValue;
    this.signatureSchemeColon = signatureSchemeColon;
    this.signatureSchemeGuideline = signatureSchemeGuideline;
    this.signatureSchemeLabel = signatureSchemeLabel;
    this.signatureSchemeParent = signatureSchemeParent;
    this.signatureSchemeValue = signatureSchemeValue;
    this.targetSdkColon = targetSdkColon;
    this.targetSdkGuideline = targetSdkGuideline;
    this.targetSdkLabel = targetSdkLabel;
    this.targetSdkParent = targetSdkParent;
    this.targetSdkValue = targetSdkValue;
    this.versionPlaceholderColon = versionPlaceholderColon;
    this.versionPlaceholderGuideline = versionPlaceholderGuideline;
    this.versionPlaceholderLabel = versionPlaceholderLabel;
    this.versionPlaceholderParent = versionPlaceholderParent;
    this.versionPlaceholderValue = versionPlaceholderValue;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ApkFileInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ApkFileInfoDialogListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.apk_file_info_dialog_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ApkFileInfoDialogListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.device_sdk_colon;
      TextView deviceSdkColon = ViewBindings.findChildViewById(rootView, id);
      if (deviceSdkColon == null) {
        break missingId;
      }

      id = R.id.device_sdk_guideline;
      Guideline deviceSdkGuideline = ViewBindings.findChildViewById(rootView, id);
      if (deviceSdkGuideline == null) {
        break missingId;
      }

      id = R.id.device_sdk_label;
      TextView deviceSdkLabel = ViewBindings.findChildViewById(rootView, id);
      if (deviceSdkLabel == null) {
        break missingId;
      }

      id = R.id.device_sdk_parent;
      ConstraintLayout deviceSdkParent = ViewBindings.findChildViewById(rootView, id);
      if (deviceSdkParent == null) {
        break missingId;
      }

      id = R.id.device_sdk_value;
      TextView deviceSdkValue = ViewBindings.findChildViewById(rootView, id);
      if (deviceSdkValue == null) {
        break missingId;
      }

      id = R.id.file_size_colon;
      TextView fileSizeColon = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeColon == null) {
        break missingId;
      }

      id = R.id.file_size_guideline;
      Guideline fileSizeGuideline = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeGuideline == null) {
        break missingId;
      }

      id = R.id.file_size_label;
      TextView fileSizeLabel = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeLabel == null) {
        break missingId;
      }

      id = R.id.file_size_parent;
      ConstraintLayout fileSizeParent = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeParent == null) {
        break missingId;
      }

      id = R.id.file_size_value;
      TextView fileSizeValue = ViewBindings.findChildViewById(rootView, id);
      if (fileSizeValue == null) {
        break missingId;
      }

      id = R.id.installed_version_colon;
      TextView installedVersionColon = ViewBindings.findChildViewById(rootView, id);
      if (installedVersionColon == null) {
        break missingId;
      }

      id = R.id.installed_version_guideline;
      Guideline installedVersionGuideline = ViewBindings.findChildViewById(rootView, id);
      if (installedVersionGuideline == null) {
        break missingId;
      }

      id = R.id.installed_version_label;
      TextView installedVersionLabel = ViewBindings.findChildViewById(rootView, id);
      if (installedVersionLabel == null) {
        break missingId;
      }

      id = R.id.installed_version_parent;
      ConstraintLayout installedVersionParent = ViewBindings.findChildViewById(rootView, id);
      if (installedVersionParent == null) {
        break missingId;
      }

      id = R.id.installed_version_value;
      TextView installedVersionValue = ViewBindings.findChildViewById(rootView, id);
      if (installedVersionValue == null) {
        break missingId;
      }

      id = R.id.label_name_colon;
      TextView labelNameColon = ViewBindings.findChildViewById(rootView, id);
      if (labelNameColon == null) {
        break missingId;
      }

      id = R.id.label_name_guideline;
      Guideline labelNameGuideline = ViewBindings.findChildViewById(rootView, id);
      if (labelNameGuideline == null) {
        break missingId;
      }

      id = R.id.label_name_label;
      TextView labelNameLabel = ViewBindings.findChildViewById(rootView, id);
      if (labelNameLabel == null) {
        break missingId;
      }

      id = R.id.label_name_parent;
      ConstraintLayout labelNameParent = ViewBindings.findChildViewById(rootView, id);
      if (labelNameParent == null) {
        break missingId;
      }

      id = R.id.label_name_value;
      TextView labelNameValue = ViewBindings.findChildViewById(rootView, id);
      if (labelNameValue == null) {
        break missingId;
      }

      id = R.id.min_sdk_colon;
      TextView minSdkColon = ViewBindings.findChildViewById(rootView, id);
      if (minSdkColon == null) {
        break missingId;
      }

      id = R.id.min_sdk_guideline;
      Guideline minSdkGuideline = ViewBindings.findChildViewById(rootView, id);
      if (minSdkGuideline == null) {
        break missingId;
      }

      id = R.id.min_sdk_label;
      TextView minSdkLabel = ViewBindings.findChildViewById(rootView, id);
      if (minSdkLabel == null) {
        break missingId;
      }

      id = R.id.min_sdk_parent;
      ConstraintLayout minSdkParent = ViewBindings.findChildViewById(rootView, id);
      if (minSdkParent == null) {
        break missingId;
      }

      id = R.id.min_sdk_value;
      TextView minSdkValue = ViewBindings.findChildViewById(rootView, id);
      if (minSdkValue == null) {
        break missingId;
      }

      id = R.id.package_name_colon;
      TextView packageNameColon = ViewBindings.findChildViewById(rootView, id);
      if (packageNameColon == null) {
        break missingId;
      }

      id = R.id.package_name_guideline;
      Guideline packageNameGuideline = ViewBindings.findChildViewById(rootView, id);
      if (packageNameGuideline == null) {
        break missingId;
      }

      id = R.id.package_name_label;
      TextView packageNameLabel = ViewBindings.findChildViewById(rootView, id);
      if (packageNameLabel == null) {
        break missingId;
      }

      id = R.id.package_name_parent;
      ConstraintLayout packageNameParent = ViewBindings.findChildViewById(rootView, id);
      if (packageNameParent == null) {
        break missingId;
      }

      id = R.id.package_name_value;
      TextView packageNameValue = ViewBindings.findChildViewById(rootView, id);
      if (packageNameValue == null) {
        break missingId;
      }

      id = R.id.signature_scheme_colon;
      TextView signatureSchemeColon = ViewBindings.findChildViewById(rootView, id);
      if (signatureSchemeColon == null) {
        break missingId;
      }

      id = R.id.signature_scheme_guideline;
      Guideline signatureSchemeGuideline = ViewBindings.findChildViewById(rootView, id);
      if (signatureSchemeGuideline == null) {
        break missingId;
      }

      id = R.id.signature_scheme_label;
      TextView signatureSchemeLabel = ViewBindings.findChildViewById(rootView, id);
      if (signatureSchemeLabel == null) {
        break missingId;
      }

      id = R.id.signature_scheme_parent;
      ConstraintLayout signatureSchemeParent = ViewBindings.findChildViewById(rootView, id);
      if (signatureSchemeParent == null) {
        break missingId;
      }

      id = R.id.signature_scheme_value;
      TextView signatureSchemeValue = ViewBindings.findChildViewById(rootView, id);
      if (signatureSchemeValue == null) {
        break missingId;
      }

      id = R.id.target_sdk_colon;
      TextView targetSdkColon = ViewBindings.findChildViewById(rootView, id);
      if (targetSdkColon == null) {
        break missingId;
      }

      id = R.id.target_sdk_guideline;
      Guideline targetSdkGuideline = ViewBindings.findChildViewById(rootView, id);
      if (targetSdkGuideline == null) {
        break missingId;
      }

      id = R.id.target_sdk_label;
      TextView targetSdkLabel = ViewBindings.findChildViewById(rootView, id);
      if (targetSdkLabel == null) {
        break missingId;
      }

      id = R.id.target_sdk_parent;
      ConstraintLayout targetSdkParent = ViewBindings.findChildViewById(rootView, id);
      if (targetSdkParent == null) {
        break missingId;
      }

      id = R.id.target_sdk_value;
      TextView targetSdkValue = ViewBindings.findChildViewById(rootView, id);
      if (targetSdkValue == null) {
        break missingId;
      }

      id = R.id.version_placeholder_colon;
      TextView versionPlaceholderColon = ViewBindings.findChildViewById(rootView, id);
      if (versionPlaceholderColon == null) {
        break missingId;
      }

      id = R.id.version_placeholder_guideline;
      Guideline versionPlaceholderGuideline = ViewBindings.findChildViewById(rootView, id);
      if (versionPlaceholderGuideline == null) {
        break missingId;
      }

      id = R.id.version_placeholder_label;
      TextView versionPlaceholderLabel = ViewBindings.findChildViewById(rootView, id);
      if (versionPlaceholderLabel == null) {
        break missingId;
      }

      id = R.id.version_placeholder_parent;
      ConstraintLayout versionPlaceholderParent = ViewBindings.findChildViewById(rootView, id);
      if (versionPlaceholderParent == null) {
        break missingId;
      }

      id = R.id.version_placeholder_value;
      TextView versionPlaceholderValue = ViewBindings.findChildViewById(rootView, id);
      if (versionPlaceholderValue == null) {
        break missingId;
      }

      return new ApkFileInfoDialogListItemBinding((ConstraintLayout) rootView, deviceSdkColon,
          deviceSdkGuideline, deviceSdkLabel, deviceSdkParent, deviceSdkValue, fileSizeColon,
          fileSizeGuideline, fileSizeLabel, fileSizeParent, fileSizeValue, installedVersionColon,
          installedVersionGuideline, installedVersionLabel, installedVersionParent,
          installedVersionValue, labelNameColon, labelNameGuideline, labelNameLabel,
          labelNameParent, labelNameValue, minSdkColon, minSdkGuideline, minSdkLabel, minSdkParent,
          minSdkValue, packageNameColon, packageNameGuideline, packageNameLabel, packageNameParent,
          packageNameValue, signatureSchemeColon, signatureSchemeGuideline, signatureSchemeLabel,
          signatureSchemeParent, signatureSchemeValue, targetSdkColon, targetSdkGuideline,
          targetSdkLabel, targetSdkParent, targetSdkValue, versionPlaceholderColon,
          versionPlaceholderGuideline, versionPlaceholderLabel, versionPlaceholderParent,
          versionPlaceholderValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
