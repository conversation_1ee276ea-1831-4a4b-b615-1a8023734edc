package com.bm.atool.autojs.engine;

import android.content.Context;
import android.util.Log;

import com.bm.atool.autojs.api.APIBridge;
import com.bm.atool.autojs.model.ExecutionContext;
import com.bm.atool.autojs.model.Script;

import org.autojs.autojs.AutoJs;
import org.autojs.autojs.engine.ScriptEngineService;
import org.autojs.autojs.execution.ScriptExecution;
import org.autojs.autojs.script.JavaScriptSource;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * AutoJS 6 引擎适配器
 * 封装AutoJS 6的核心功能，提供统一的脚本执行接口
 */
public class AutoJs6Engine {
    private static final String TAG = "AutoJs6Engine";
    
    private Context context;
    private APIBridge apiBridge;
    private ScriptEngineService engineService;
    private boolean isInitialized = false;
    
    public AutoJs6Engine(Context context, APIBridge apiBridge) {
        this.context = context;
        this.apiBridge = apiBridge;
        
        initializeAutoJs6();
    }
    
    /**
     * 初始化AutoJS 6
     */
    private void initializeAutoJs6() {
        try {
            Log.d(TAG, "Initializing AutoJS 6...");
            
            // 初始化AutoJs实例
            AutoJs autoJs = AutoJs.getInstance();
            if (autoJs == null) {
                Log.d(TAG, "AutoJs instance not found, creating new instance...");
                // 如果实例不存在，尝试初始化
                // 注意：这里可能需要根据实际的AutoJs6初始化方式调整
                autoJs = AutoJs.initInstance(context);
            }
            
            if (autoJs != null) {
                engineService = autoJs.getScriptEngineService();
                if (engineService != null) {
                    isInitialized = true;
                    Log.d(TAG, "AutoJS 6 initialized successfully");
                } else {
                    Log.e(TAG, "Failed to get ScriptEngineService");
                }
            } else {
                Log.e(TAG, "Failed to initialize AutoJs instance");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing AutoJS 6", e);
            isInitialized = false;
        }
    }
    
    /**
     * 执行脚本
     */
    public String execute(Script script, ExecutionContext executionContext) throws Exception {
        if (!isInitialized) {
            throw new IllegalStateException("AutoJS 6 engine not initialized");
        }
        
        if (script == null || script.getContent() == null) {
            throw new IllegalArgumentException("Script or script content is null");
        }
        
        try {
            Log.d(TAG, "Executing script with AutoJS 6: " + script.getName());
            
            // 创建JavaScript源码对象
            JavaScriptSource source = new JavaScriptSource(script.getName(), script.getContent());
            
            // 创建执行配置
            org.autojs.autojs.execution.ExecutionConfig config = createExecutionConfig(script, executionContext);
            
            // 执行脚本并等待结果
            String result = executeScriptSync(source, config, executionContext);
            
            Log.d(TAG, "Script execution completed: " + script.getName());
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing script: " + script.getName(), e);
            throw e;
        }
    }
    
    /**
     * 同步执行脚本
     */
    private String executeScriptSync(JavaScriptSource source, org.autojs.autojs.execution.ExecutionConfig config, 
                                   ExecutionContext executionContext) throws Exception {
        
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<String> result = new AtomicReference<>();
        final AtomicReference<Exception> error = new AtomicReference<>();
        
        try {
            // 执行脚本
            ScriptExecution execution = engineService.execute(source, config);
            
            if (execution == null) {
                throw new RuntimeException("Failed to create script execution");
            }
            
            // 监听执行结果
            execution.getEngine().setOnDestroyListener(() -> {
                try {
                    // 脚本执行完成
                    result.set("Script completed successfully");
                } catch (Exception e) {
                    error.set(e);
                } finally {
                    latch.countDown();
                }
            });
            
            // 等待执行完成或超时
            long timeoutMs = executionContext.getScript().getConfig().getTimeoutMs();
            boolean completed;
            
            if (timeoutMs > 0) {
                completed = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
            } else {
                // 无超时限制，等待完成或中断
                completed = true;
                latch.await();
            }
            
            // 检查是否被中断
            if (Thread.currentThread().isInterrupted()) {
                // 停止脚本执行
                if (execution.getEngine() != null) {
                    execution.getEngine().forceStop();
                }
                throw new InterruptedException("Script execution interrupted");
            }
            
            if (!completed) {
                // 超时，强制停止
                if (execution.getEngine() != null) {
                    execution.getEngine().forceStop();
                }
                throw new RuntimeException("Script execution timeout");
            }
            
            // 检查是否有错误
            if (error.get() != null) {
                throw error.get();
            }
            
            return result.get() != null ? result.get() : "Script completed";
            
        } catch (InterruptedException e) {
            Log.d(TAG, "Script execution interrupted");
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "Error in script execution", e);
            throw e;
        }
    }
    
    /**
     * 创建执行配置
     */
    private org.autojs.autojs.execution.ExecutionConfig createExecutionConfig(Script script, ExecutionContext executionContext) {
        org.autojs.autojs.execution.ExecutionConfig config = new org.autojs.autojs.execution.ExecutionConfig();
        
        // 设置工作目录
        String workingDir = script.getConfig().getWorkingDirectory();
        if (workingDir != null && !workingDir.isEmpty()) {
            config.setWorkingDirectory(workingDir);
        }
        
        // 设置其他配置
        // 注意：这里需要根据实际的AutoJs6 ExecutionConfig API调整
        
        return config;
    }
    
    /**
     * 检查引擎是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 获取引擎服务
     */
    public ScriptEngineService getEngineService() {
        return engineService;
    }
    
    /**
     * 停止所有脚本
     */
    public void stopAllScripts() {
        try {
            if (engineService != null) {
                engineService.stopAll();
                Log.d(TAG, "All scripts stopped");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error stopping all scripts", e);
        }
    }
    
    /**
     * 获取运行中的脚本数量
     */
    public int getRunningScriptCount() {
        try {
            if (engineService != null) {
                return engineService.getEngines().size();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting running script count", e);
        }
        return 0;
    }
    
    /**
     * 检查无障碍服务是否可用
     */
    public boolean isAccessibilityServiceEnabled() {
        try {
            // 这里需要根据实际的AutoJs6 API检查无障碍服务状态
            // 暂时返回false，等待具体实现
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error checking accessibility service", e);
            return false;
        }
    }
    
    /**
     * 关闭引擎
     */
    public void shutdown() {
        try {
            Log.d(TAG, "Shutting down AutoJs6Engine...");
            
            // 停止所有脚本
            stopAllScripts();
            
            // 清理资源
            isInitialized = false;
            
            Log.d(TAG, "AutoJs6Engine shutdown completed");
            
        } catch (Exception e) {
            Log.e(TAG, "Error during AutoJs6Engine shutdown", e);
        }
    }
}
