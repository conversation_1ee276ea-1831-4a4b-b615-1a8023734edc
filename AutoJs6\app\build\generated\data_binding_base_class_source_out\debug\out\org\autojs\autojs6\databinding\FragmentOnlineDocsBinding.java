// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import java.lang.NullPointerException;
import java.lang.Override;
import org.autojs.autojs.ui.widget.EWebView;
import org.autojs.autojs6.R;

public final class FragmentOnlineDocsBinding implements ViewBinding {
  @NonNull
  private final EWebView rootView;

  @NonNull
  public final EWebView ewebView;

  private FragmentOnlineDocsBinding(@NonNull EWebView rootView, @NonNull EWebView ewebView) {
    this.rootView = rootView;
    this.ewebView = ewebView;
  }

  @Override
  @NonNull
  public EWebView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentOnlineDocsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentOnlineDocsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_online_docs, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentOnlineDocsBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    EWebView ewebView = (EWebView) rootView;

    return new FragmentOnlineDocsBinding((EWebView) rootView, ewebView);
  }
}
