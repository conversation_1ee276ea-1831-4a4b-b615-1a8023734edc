package com.bm.atool.ui;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.R;
import com.bm.autojs6.module.AutoJs6Module;
import com.bm.autojs6.module.model.Script;
import com.bm.autojs6.module.model.ScriptResult;

/**
 * 简化的AutoJs脚本执行Fragment
 * 提供基本的JS脚本编辑和执行功能
 */
public class SimpleAutoJsFragment extends BaseFragment {
    private static final String TAG = "SimpleAutoJsFragment";
    
    private EditText etScriptContent;
    private EditText etScriptName;
    private Button btnExecuteScript;
    private Button btnStopAllScripts;
    private Button btnCheckStatus;
    private TextView tvScriptStatus;
    private TextView tvRunningScripts;
    
    private AutoJs6Module autoJs6Module;
    private Handler uiHandler;
    
    public SimpleAutoJsFragment() {
        super();
        this.setTitle("JS脚本执行器");
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_simple_autojs, container, false);
        
        initViews(view);
        initAutoJs();
        setupClickListeners();
        updateUI();
        
        return view;
    }
    
    private void initViews(View view) {
        etScriptContent = view.findViewById(R.id.etScriptContent);
        etScriptName = view.findViewById(R.id.etScriptName);
        btnExecuteScript = view.findViewById(R.id.btnExecuteScript);
        btnStopAllScripts = view.findViewById(R.id.btnStopAllScripts);
        btnCheckStatus = view.findViewById(R.id.btnCheckStatus);
        tvScriptStatus = view.findViewById(R.id.tvScriptStatus);
        tvRunningScripts = view.findViewById(R.id.tvRunningScripts);
        
        uiHandler = new Handler();
        
        // 设置默认脚本内容
        setDefaultScript();
    }
    
    private void initAutoJs() {
        autoJs6Module = AutoJs6Module.getInstance();
        if (!autoJs6Module.isInitialized()) {
            autoJs6Module.initialize(getContext());
            Log.d(TAG, "AutoJs6Module initialized");
        } else {
            Log.d(TAG, "AutoJs6Module already initialized");
        }
    }
    
    private void setupClickListeners() {
        btnExecuteScript.setOnClickListener(v -> executeScript());
        btnStopAllScripts.setOnClickListener(v -> stopAllScripts());
        btnCheckStatus.setOnClickListener(v -> checkStatus());
    }
    
    private void setDefaultScript() {
        String defaultScript = "// AutoJs6 Module 测试脚本\n" +
                "console.log('Hello AutoJs6 Module!');\n\n" +
                "// 计算示例\n" +
                "var a = 10;\n" +
                "var b = 20;\n" +
                "var result = a + b;\n" +
                "console.log('计算结果: ' + a + ' + ' + b + ' = ' + result);\n\n" +
                "// 循环示例\n" +
                "for (var i = 1; i <= 3; i++) {\n" +
                "    console.log('循环第 ' + i + ' 次');\n" +
                "}\n\n" +
                "// 函数定义和调用\n" +
                "function greet(name) {\n" +
                "    return 'Hello, ' + name + '!';\n" +
                "}\n\n" +
                "var greeting = greet('AutoJs6');\n" +
                "console.log(greeting);\n\n" +
                "// 对象操作\n" +
                "var person = {\n" +
                "    name: '张三',\n" +
                "    age: 25\n" +
                "};\n" +
                "console.log('姓名: ' + person.name + ', 年龄: ' + person.age);\n\n" +
                "// 测试utils工具函数\n" +
                "if (typeof utils !== 'undefined') {\n" +
                "    utils.log('这是通过utils.log输出的消息');\n" +
                "} else {\n" +
                "    console.log('utils对象未定义');\n" +
                "}\n\n" +
                "console.log('脚本执行完成!');\n" +
                "'脚本执行成功 - 所有功能正常';";

        etScriptContent.setText(defaultScript);
        etScriptName.setText("AutoJs6测试脚本");
    }
    
    private void executeScript() {
        String scriptContent = etScriptContent.getText().toString().trim();
        String scriptNameTemp = etScriptName.getText().toString().trim();

        if (TextUtils.isEmpty(scriptContent)) {
            showToast("请输入脚本内容");
            return;
        }

        final String scriptName = TextUtils.isEmpty(scriptNameTemp) ? "未命名脚本" : scriptNameTemp;
        
        if (!autoJs6Module.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }
        
        try {
            // 创建脚本对象
            Script script = new Script(scriptName, scriptContent);

            // 异步执行脚本
            autoJs6Module.executeScriptAsync(script)
                .thenAccept(result -> {
                    uiHandler.post(() -> {
                        if (result.isSuccess()) {
                            tvScriptStatus.setText("脚本执行成功: " + scriptName);
                            showToast("脚本执行成功: " + result.getResult());
                        } else {
                            tvScriptStatus.setText("脚本执行失败: " + scriptName);
                            showToast("脚本执行失败: " + result.getError());
                        }
                        updateRunningScriptsCount();
                    });
                })
                .exceptionally(throwable -> {
                    uiHandler.post(() -> {
                        tvScriptStatus.setText("脚本执行异常: " + scriptName);
                        showToast("脚本执行异常: " + throwable.getMessage());
                        updateRunningScriptsCount();
                    });
                    return null;
                });

            showToast("脚本开始执行");
            tvScriptStatus.setText("脚本正在执行: " + scriptName);
            updateRunningScriptsCount();

        } catch (Exception e) {
            Log.e(TAG, "Error executing script", e);
            showToast("脚本执行出错: " + e.getMessage());
            tvScriptStatus.setText("脚本执行出错: " + e.getMessage());
        }
    }
    
    private void stopAllScripts() {
        try {
            autoJs6Module.stopAllScripts();
            showToast("已停止所有脚本");
            tvScriptStatus.setText("已停止所有脚本");
            updateRunningScriptsCount();
        } catch (Exception e) {
            Log.e(TAG, "Error stopping scripts", e);
            showToast("停止脚本失败: " + e.getMessage());
        }
    }
    
    private void checkStatus() {
        if (!autoJs6Module.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }

        int runningCount = autoJs6Module.getRunningScriptCount();

        String status = String.format("JS引擎状态: 正常\n运行中的脚本: %d",
            runningCount);

        showToast("JS引擎运行正常");
        tvScriptStatus.setText(status);
        updateRunningScriptsCount();
    }
    
    private void updateRunningScriptsCount() {
        try {
            int count = autoJs6Module.getRunningScriptCount();
            tvRunningScripts.setText("运行中的脚本: " + count);
        } catch (Exception e) {
            Log.e(TAG, "Error getting running script count", e);
            tvRunningScripts.setText("运行中的脚本: 未知");
        }
    }
    
    private void updateUI() {
        updateRunningScriptsCount();
        
        if (autoJs6Module.isInitialized()) {
            tvScriptStatus.setText("JS引擎已就绪");
        } else {
            tvScriptStatus.setText("JS引擎未初始化");
        }
    }
    
    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        updateUI();
    }
    
    @Override
    public int getIconResourceId() {
        return R.drawable.ic_code;
    }
}
