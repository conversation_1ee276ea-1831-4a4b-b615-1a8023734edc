// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.core.console.FloatingConsoleView;
import org.autojs.autojs6.R;

public final class FloatingConsoleExpandBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView close;

  @NonNull
  public final FloatingConsoleView console;

  @NonNull
  public final ImageView minimize;

  @NonNull
  public final ImageView moveCursor;

  @NonNull
  public final ImageView moveOrResize;

  @NonNull
  public final ImageView resizer;

  @NonNull
  public final TextView title;

  @NonNull
  public final LinearLayout titleBar;

  private FloatingConsoleExpandBinding(@NonNull RelativeLayout rootView, @NonNull ImageView close,
      @NonNull FloatingConsoleView console, @NonNull ImageView minimize,
      @NonNull ImageView moveCursor, @NonNull ImageView moveOrResize, @NonNull ImageView resizer,
      @NonNull TextView title, @NonNull LinearLayout titleBar) {
    this.rootView = rootView;
    this.close = close;
    this.console = console;
    this.minimize = minimize;
    this.moveCursor = moveCursor;
    this.moveOrResize = moveOrResize;
    this.resizer = resizer;
    this.title = title;
    this.titleBar = titleBar;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FloatingConsoleExpandBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FloatingConsoleExpandBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.floating_console_expand, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FloatingConsoleExpandBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.close;
      ImageView close = ViewBindings.findChildViewById(rootView, id);
      if (close == null) {
        break missingId;
      }

      id = R.id.console;
      FloatingConsoleView console = ViewBindings.findChildViewById(rootView, id);
      if (console == null) {
        break missingId;
      }

      id = R.id.minimize;
      ImageView minimize = ViewBindings.findChildViewById(rootView, id);
      if (minimize == null) {
        break missingId;
      }

      id = R.id.move_cursor;
      ImageView moveCursor = ViewBindings.findChildViewById(rootView, id);
      if (moveCursor == null) {
        break missingId;
      }

      id = R.id.move_or_resize;
      ImageView moveOrResize = ViewBindings.findChildViewById(rootView, id);
      if (moveOrResize == null) {
        break missingId;
      }

      id = R.id.resizer;
      ImageView resizer = ViewBindings.findChildViewById(rootView, id);
      if (resizer == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      id = R.id.title_bar;
      LinearLayout titleBar = ViewBindings.findChildViewById(rootView, id);
      if (titleBar == null) {
        break missingId;
      }

      return new FloatingConsoleExpandBinding((RelativeLayout) rootView, close, console, minimize,
          moveCursor, moveOrResize, resizer, title, titleBar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
