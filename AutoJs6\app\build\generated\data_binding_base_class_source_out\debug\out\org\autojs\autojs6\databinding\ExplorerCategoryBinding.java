// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ExplorerCategoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView arrowIcon;

  @NonNull
  public final ImageView goUp;

  @NonNull
  public final ImageView sortOrder;

  @NonNull
  public final ImageView sortType;

  @NonNull
  public final TextView title;

  @NonNull
  public final LinearLayout titleContainer;

  private ExplorerCategoryBinding(@NonNull LinearLayout rootView, @NonNull ImageView arrowIcon,
      @NonNull ImageView goUp, @NonNull ImageView sortOrder, @NonNull ImageView sortType,
      @NonNull TextView title, @NonNull LinearLayout titleContainer) {
    this.rootView = rootView;
    this.arrowIcon = arrowIcon;
    this.goUp = goUp;
    this.sortOrder = sortOrder;
    this.sortType = sortType;
    this.title = title;
    this.titleContainer = titleContainer;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ExplorerCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ExplorerCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.explorer_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ExplorerCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.arrow_icon;
      ImageView arrowIcon = ViewBindings.findChildViewById(rootView, id);
      if (arrowIcon == null) {
        break missingId;
      }

      id = R.id.go_up;
      ImageView goUp = ViewBindings.findChildViewById(rootView, id);
      if (goUp == null) {
        break missingId;
      }

      id = R.id.sort_order;
      ImageView sortOrder = ViewBindings.findChildViewById(rootView, id);
      if (sortOrder == null) {
        break missingId;
      }

      id = R.id.sort_type;
      ImageView sortType = ViewBindings.findChildViewById(rootView, id);
      if (sortType == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      id = R.id.title_container;
      LinearLayout titleContainer = ViewBindings.findChildViewById(rootView, id);
      if (titleContainer == null) {
        break missingId;
      }

      return new ExplorerCategoryBinding((LinearLayout) rootView, arrowIcon, goUp, sortOrder,
          sortType, title, titleContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
