package com.bm.autojs6.module.model;

/**
 * 脚本模型
 */
public class Script {
    private String name;
    private String content;
    private String description;
    private long createTime;
    
    public Script(String name, String content) {
        this.name = name;
        this.content = content;
        this.createTime = System.currentTimeMillis();
    }
    
    public Script(String name, String content, String description) {
        this(name, content);
        this.description = description;
    }
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public long getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "Script{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
