// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class ConsoleViewBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final EditText input;

  @NonNull
  public final LinearLayout inputContainer;

  @NonNull
  public final RecyclerView logList;

  @NonNull
  public final Button submit;

  private ConsoleViewBinding(@NonNull LinearLayout rootView, @NonNull EditText input,
      @NonNull LinearLayout inputContainer, @NonNull RecyclerView logList, @NonNull Button submit) {
    this.rootView = rootView;
    this.input = input;
    this.inputContainer = inputContainer;
    this.logList = logList;
    this.submit = submit;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ConsoleViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ConsoleViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.console_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ConsoleViewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.input;
      EditText input = ViewBindings.findChildViewById(rootView, id);
      if (input == null) {
        break missingId;
      }

      id = R.id.input_container;
      LinearLayout inputContainer = ViewBindings.findChildViewById(rootView, id);
      if (inputContainer == null) {
        break missingId;
      }

      id = R.id.log_list;
      RecyclerView logList = ViewBindings.findChildViewById(rootView, id);
      if (logList == null) {
        break missingId;
      }

      id = R.id.submit;
      Button submit = ViewBindings.findChildViewById(rootView, id);
      if (submit == null) {
        break missingId;
      }

      return new ConsoleViewBinding((LinearLayout) rootView, input, inputContainer, logList,
          submit);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
