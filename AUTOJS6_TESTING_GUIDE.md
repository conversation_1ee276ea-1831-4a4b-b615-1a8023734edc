# AutoJs6 测试指南

## 概述

本文档详细说明如何测试 AutoJs6 功能是否正常运行，包括自动化测试和手动测试方法。

## 修复的问题

### 问题1：AutoJs6 引擎被禁用
- **问题描述**: ExecutionEngine.java 中 AutoJs6Engine 被注释掉，使用模拟执行
- **修复方案**: 启用 AutoJs6Engine，使用 Rhino JavaScript 引擎提供真实的脚本执行功能
- **修复文件**: 
  - `app/src/main/java/com/bm/atool/autojs/engine/ExecutionEngine.java`
  - `app/src/main/java/com/bm/atool/autojs/engine/AutoJs6Engine.java`

### 问题2：缺少必要依赖
- **问题描述**: 缺少 Rhino JavaScript 引擎依赖
- **修复方案**: 在 app/build.gradle 中添加 Rhino 依赖
- **修复内容**: `implementation 'org.mozilla:rhino:1.7.14'`

## 自动化测试

### 1. 运行测试脚本

#### Linux/macOS:
```bash
chmod +x test_autojs6.sh
./test_autojs6.sh
```

#### Windows:
```cmd
test_autojs6.bat
```

### 2. 单元测试
```bash
./gradlew test
```

### 3. 集成测试（需要连接设备）
```bash
./gradlew connectedAndroidTest
```

## 手动测试

### 1. 构建和安装应用

```bash
# 清理项目
./gradlew clean

# 构建项目
./gradlew build

# 安装到设备
./gradlew installDebug
```

### 2. 应用内测试

1. **启动应用**
   - 打开 android-tool 应用
   - 确保所有必要权限已授予

2. **切换到 JS 脚本执行器**
   - 点击底部导航栏的第三个标签（代码图标）
   - 进入 "JavaScript 脚本执行器" 页面

3. **测试默认脚本**
   - 页面会显示预设的测试脚本
   - 点击 "执行脚本" 按钮
   - 观察执行结果

4. **验证功能**
   - 检查 "脚本状态" 显示执行成功
   - 检查 "运行中的脚本" 计数变化
   - 观察 Toast 消息提示

### 3. 测试脚本验证

#### 基础测试脚本 (test_scripts/simple_test.js)
```javascript
// 测试基本功能
console.log("Hello AutoJs6 Module!");
utils.log("这是通过utils.log输出的消息");

// 测试计算
var a = 10;
var b = 20;
var result = a + b;
console.log("计算结果: " + a + " + " + b + " = " + result);

// 返回结果
"脚本执行成功 - 所有测试通过";
```

#### 框架深度测试 (test_scripts/autojs6_framework_test.js)
- 测试复杂数据结构
- 测试高级函数和闭包
- 测试递归算法
- 测试字符串处理
- 测试数学运算
- 测试错误处理
- 测试性能

### 4. 自定义脚本测试

在应用中输入以下测试脚本：

```javascript
// 简单测试
console.log("AutoJs6 测试开始");

// 测试变量
var message = "Hello AutoJs6!";
console.log(message);

// 测试函数
function test() {
    return "函数调用成功";
}
console.log(test());

// 测试utils
if (typeof utils !== 'undefined') {
    utils.log("Utils 功能正常");
}

console.log("测试完成");
"自定义脚本执行成功";
```

## 预期结果

### 成功指标

1. **脚本执行成功**
   - 状态显示 "脚本执行成功"
   - Toast 提示 "脚本执行成功"
   - 无错误消息

2. **控制台输出正常**
   - 所有 console.log 消息正确显示
   - utils.log 消息正确输出
   - 计算结果正确

3. **功能验证**
   - 变量赋值和计算正常
   - 函数定义和调用正常
   - 对象操作正常
   - 数组操作正常
   - 字符串处理正常

### 失败指标

1. **脚本执行失败**
   - 状态显示错误消息
   - Toast 提示执行失败
   - 控制台显示异常信息

2. **功能异常**
   - JavaScript 语法错误
   - 运行时异常
   - API 调用失败

## 故障排除

### 常见问题

1. **编译错误**
   ```
   解决方案: 检查 Rhino 依赖是否正确添加到 build.gradle
   ```

2. **脚本执行失败**
   ```
   解决方案: 检查 AutoJs6Engine 是否正确初始化
   ```

3. **权限问题**
   ```
   解决方案: 确保应用已获得必要的系统权限
   ```

### 调试方法

1. **查看日志**
   ```bash
   adb logcat | grep -E "(AutoJs|ScriptEngine|ExecutionEngine)"
   ```

2. **检查组件状态**
   - 在应用中点击 "检查状态" 按钮
   - 查看 JS 引擎初始化状态

3. **逐步测试**
   - 从简单脚本开始测试
   - 逐步增加复杂度
   - 定位具体问题

## 性能测试

### 基准测试
- 简单脚本执行时间: < 100ms
- 复杂脚本执行时间: < 1000ms
- 内存使用: 正常范围内

### 压力测试
- 连续执行多个脚本
- 长时间运行脚本
- 大数据量处理

## 总结

通过以上测试方法，可以全面验证 AutoJs6 功能是否正常运行。建议按照以下顺序进行测试：

1. 运行自动化测试脚本
2. 手动测试基础功能
3. 测试复杂脚本
4. 验证性能表现

如果所有测试都通过，说明 AutoJs6 已经可以正常运行 JavaScript 脚本。
