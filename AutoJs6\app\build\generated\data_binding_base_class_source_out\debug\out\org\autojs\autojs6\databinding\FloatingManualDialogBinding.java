// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.widget.EWebView;
import org.autojs.autojs6.R;

public final class FloatingManualDialogBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView close;

  @NonNull
  public final EWebView ewebView;

  @NonNull
  public final ImageView fullscreen;

  @NonNull
  public final ImageView pinToLeft;

  @NonNull
  public final TextView title;

  private FloatingManualDialogBinding(@NonNull CardView rootView, @NonNull ImageView close,
      @NonNull EWebView ewebView, @NonNull ImageView fullscreen, @NonNull ImageView pinToLeft,
      @NonNull TextView title) {
    this.rootView = rootView;
    this.close = close;
    this.ewebView = ewebView;
    this.fullscreen = fullscreen;
    this.pinToLeft = pinToLeft;
    this.title = title;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static FloatingManualDialogBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FloatingManualDialogBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.floating_manual_dialog, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FloatingManualDialogBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.close;
      ImageView close = ViewBindings.findChildViewById(rootView, id);
      if (close == null) {
        break missingId;
      }

      id = R.id.eweb_view;
      EWebView ewebView = ViewBindings.findChildViewById(rootView, id);
      if (ewebView == null) {
        break missingId;
      }

      id = R.id.fullscreen;
      ImageView fullscreen = ViewBindings.findChildViewById(rootView, id);
      if (fullscreen == null) {
        break missingId;
      }

      id = R.id.pin_to_left;
      ImageView pinToLeft = ViewBindings.findChildViewById(rootView, id);
      if (pinToLeft == null) {
        break missingId;
      }

      id = R.id.title;
      TextView title = ViewBindings.findChildViewById(rootView, id);
      if (title == null) {
        break missingId;
      }

      return new FloatingManualDialogBinding((CardView) rootView, close, ewebView, fullscreen,
          pinToLeft, title);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
