// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs.ui.widget.ToolbarMenuItem;
import org.autojs.autojs6.R;

public final class FragmentDebugToolbarBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ToolbarMenuItem forceStop;

  @NonNull
  public final ToolbarMenuItem resume;

  @NonNull
  public final ToolbarMenuItem stepInto;

  @NonNull
  public final ToolbarMenuItem stepOut;

  @NonNull
  public final ToolbarMenuItem stepOver;

  private FragmentDebugToolbarBinding(@NonNull LinearLayout rootView,
      @NonNull ToolbarMenuItem forceStop, @NonNull ToolbarMenuItem resume,
      @NonNull ToolbarMenuItem stepInto, @NonNull ToolbarMenuItem stepOut,
      @NonNull ToolbarMenuItem stepOver) {
    this.rootView = rootView;
    this.forceStop = forceStop;
    this.resume = resume;
    this.stepInto = stepInto;
    this.stepOut = stepOut;
    this.stepOver = stepOver;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDebugToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDebugToolbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_debug_toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDebugToolbarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.force_stop;
      ToolbarMenuItem forceStop = ViewBindings.findChildViewById(rootView, id);
      if (forceStop == null) {
        break missingId;
      }

      id = R.id.resume;
      ToolbarMenuItem resume = ViewBindings.findChildViewById(rootView, id);
      if (resume == null) {
        break missingId;
      }

      id = R.id.step_into;
      ToolbarMenuItem stepInto = ViewBindings.findChildViewById(rootView, id);
      if (stepInto == null) {
        break missingId;
      }

      id = R.id.step_out;
      ToolbarMenuItem stepOut = ViewBindings.findChildViewById(rootView, id);
      if (stepOut == null) {
        break missingId;
      }

      id = R.id.step_over;
      ToolbarMenuItem stepOver = ViewBindings.findChildViewById(rootView, id);
      if (stepOver == null) {
        break missingId;
      }

      return new FragmentDebugToolbarBinding((LinearLayout) rootView, forceStop, resume, stepInto,
          stepOut, stepOver);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
