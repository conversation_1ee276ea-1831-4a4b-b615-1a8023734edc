// Generated by view binder compiler. Do not edit!
package org.autojs.autojs6.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;
import org.autojs.autojs6.R;

public final class FloatyWindowBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageView close;

  @NonNull
  public final FrameLayout container;

  @NonNull
  public final ImageView moveCursor;

  @NonNull
  public final ImageView resizer;

  private FloatyWindowBinding(@NonNull FrameLayout rootView, @NonNull ImageView close,
      @NonNull FrameLayout container, @NonNull ImageView moveCursor, @NonNull ImageView resizer) {
    this.rootView = rootView;
    this.close = close;
    this.container = container;
    this.moveCursor = moveCursor;
    this.resizer = resizer;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FloatyWindowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FloatyWindowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.floaty_window, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FloatyWindowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.close;
      ImageView close = ViewBindings.findChildViewById(rootView, id);
      if (close == null) {
        break missingId;
      }

      id = R.id.container;
      FrameLayout container = ViewBindings.findChildViewById(rootView, id);
      if (container == null) {
        break missingId;
      }

      id = R.id.move_cursor;
      ImageView moveCursor = ViewBindings.findChildViewById(rootView, id);
      if (moveCursor == null) {
        break missingId;
      }

      id = R.id.resizer;
      ImageView resizer = ViewBindings.findChildViewById(rootView, id);
      if (resizer == null) {
        break missingId;
      }

      return new FloatyWindowBinding((FrameLayout) rootView, close, container, moveCursor, resizer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
